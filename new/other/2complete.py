import cv2
import numpy as np
import os
from pathlib import Path
from scipy.ndimage import binary_dilation

# ====== 参数配置 ======
IMAGE_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0617/images/val"
LABEL_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0617/labels/val"

# IMAGE_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/test"
# LABEL_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/test"

OUTPUT_DIR = "output"
AREA_THRESH = 300

class_names = {
    0: "bed_grounded", 1: "bed_highleg",
    2: "sofa_grounded", 3: "sofa_highleg",
    4: "door", 5: "dining_table_set"
}

room_type_map = {
    0: "bedroom", 1: "bedroom",
    2: "livingroom", 3: "livingroom",
    5: "diningroom"
}

room_color_map = {
    "bedroom": (128, 128, 255),
    "livingroom": (128, 255, 128),
    "diningroom": (255, 128, 128),
    "unknow": (255, 192, 0)
}

room_label_map = {
    "bedroom": 1,
    "livingroom": 2,
    "diningroom": 3,
    "unknow": 0
}

def load_yolo_labels(label_file, img_w, img_h):
    boxes, classes = [], []
    with open(label_file, 'r') as f:
        for line in f:
            cls, cx, cy, w, h = map(float, line.strip().split())
            cls = int(cls)
            x1 = int((cx - w / 2) * img_w)
            y1 = int((cy - h / 2) * img_h)
            x2 = int((cx + w / 2) * img_w)
            y2 = int((cy + h / 2) * img_h)
            boxes.append([x1, y1, x2, y2])
            classes.append(cls)
    return boxes, classes

def process_image(image_path, label_path, save_dir):
    img_name = Path(image_path).stem
    image = cv2.imread(image_path)
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    h, w = gray.shape

    boxes, classes = load_yolo_labels(label_path, w, h)

    _, binary = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)

    for (x1, y1, x2, y2), cls in zip(boxes, classes):
        if cls == 4:  # door
            cv2.rectangle(binary, (x1, y1), (x2, y2 + 1), 0, -1)
        if cls in [0, 2]:  # bed_grounded or sofa_grounded
            cv2.rectangle(binary, (x1, y1), (x2, y2), 255, -1)

    num_labels, labels_im = cv2.connectedComponents(binary)

    # ===== 合并小区域到大区域 =====
    region_masks, region_areas = {}, {}
    for i in range(1, num_labels):
        mask = (labels_im == i)
        region_masks[i] = mask
        region_areas[i] = np.sum(mask)

    merged_labels = labels_im.copy()
    label_list = sorted(region_areas.items(), key=lambda x: x[1], reverse=True)

    for big_label, _ in label_list:
        big_mask = region_masks[big_label]
        big_mask_dilated = binary_dilation(big_mask, iterations=5)

        for small_label, _ in label_list:
            if small_label == big_label or region_areas[small_label] == 0:
                continue
            small_mask = region_masks[small_label]
            overlap = big_mask_dilated & small_mask
            if np.sum(overlap) > 0.3 * np.sum(small_mask):
                merged_labels[merged_labels == small_label] = big_label
                region_masks[big_label] = (merged_labels == big_label)
                region_areas[big_label] += region_areas[small_label]
                region_areas[small_label] = 0

    labels_im = merged_labels

    vis_image = image.copy()
    label_mask = np.zeros((h, w), dtype=np.uint8)
    font = cv2.FONT_HERSHEY_SIMPLEX
    room_idx = 0

    for i in range(1, num_labels):
        mask = (labels_im == i)
        if np.sum(mask) < AREA_THRESH:
            continue

        present_classes = set()
        for (x1, y1, x2, y2), cls in zip(boxes, classes):
            if cls == 4:
                continue
            bx, by = (x1 + x2) // 2, (y1 + y2) // 2
            if 0 <= bx < w and 0 <= by < h and mask[by, bx]:
                present_classes.add(cls)

        # 推理 room type
        if 5 in present_classes and (2 in present_classes or 3 in present_classes):
            room_type = "livingroom"
        else:
            room_type = "unknow"
            for cls in present_classes:
                if cls in room_type_map:
                    room_type = room_type_map[cls]
                    break

        color = room_color_map[room_type]
        vis_image[mask] = cv2.addWeighted(vis_image, 0.5, np.full_like(image, color), 0.7, 0)[mask]

        label_value = room_label_map[room_type]
        label_mask[mask] = label_value  # <== 写入语义标签图

        ys, xs = np.where(mask)
        cx, cy = int(np.mean(xs)), int(np.mean(ys))
        cv2.putText(vis_image, f"{room_type}", (cx - 40, cy), font, 0.5, (0, 0, 0), 2)

        room_idx += 1

    # 保存结果图
    cv2.imwrite(f"{save_dir}/{img_name}_labelIds.png", label_mask)  # labelIds（Cityscapes风格）
    cv2.imwrite(f"{save_dir}/{img_name}_leftImg8bit.png", image)    # 原图
    cv2.imwrite(f"{save_dir}/overlay.png", vis_image)

    print(f"✅ {img_name} done")

def batch_process():
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    for img_file in sorted(os.listdir(IMAGE_DIR)):
        if not img_file.lower().endswith((".jpg", ".png")):
            continue
        img_path = os.path.join(IMAGE_DIR, img_file)
        label_path = os.path.join(LABEL_DIR, Path(img_file).with_suffix(".txt"))
        if not os.path.exists(label_path):
            print(f"⚠️ Missing label for {img_file}")
            continue
        save_dir = os.path.join(OUTPUT_DIR, Path(img_file).stem)
        os.makedirs(save_dir, exist_ok=True)
        process_image(img_path, label_path, save_dir)

if __name__ == "__main__":
    batch_process()