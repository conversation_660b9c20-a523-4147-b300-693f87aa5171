
import cv2
import numpy as np
from skimage.segmentation import find_boundaries

def extract_diningroom_with_guaranteed_table(
    image_path, 
    table_yolo_label, 
    output_path="diningroom_guaranteed_result.png"
):
    """确保最终分割结果必定包含dining_table_set区域的版本"""
    # === 1. 基础处理 ===
    image_gray = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    h, w = image_gray.shape

    # 解析YOLO标签
    _, cx, cy, bw, bh = map(float, table_yolo_label)
    x1, y1 = int((cx - bw/2)*w), int((cy - bh/2)*h)
    x2, y2 = int((cx + bw/2)*w), int((cy + bh/2)*h)
    x1, y1, x2, y2 = np.clip([x1, y1, x2, y2], 0, [w-1, h-1, w-1, h-1])
    table_center = ((x1+x2)//2, (y1+y2)//2)

    # === 2. 获取连通域 ===
    walkable = ((image_gray > 240) & (cv2.Canny(image_gray, 50, 150) == 0)).astype(np.uint8)
    _, labels = cv2.connectedComponents(walkable)
    if (table_label := labels[table_center[1], table_center[0]]) == 0:
        raise ValueError("餐桌不在可通行区域")
    connected_mask = (labels == table_label).astype(np.uint8)

    # === 3. 生成候选区域 ===
    expand_w, expand_h = int(bw*w*1.8), int(bh*h*1.8)
    candidate_x1 = max(0, table_center[0] - expand_w//2)
    candidate_y1 = max(0, table_center[1] - expand_h//2)
    candidate_x2 = min(w, table_center[0] + expand_w//2)
    candidate_y2 = min(h, table_center[1] + expand_h//2)
    
    # === 4. 局部优化 ===
    local_region = walkable[candidate_y1:candidate_y2, candidate_x1:candidate_x2]
    table_local = np.zeros_like(local_region)
    lx1, ly1 = max(0, x1-candidate_x1), max(0, y1-candidate_y1)
    lx2, ly2 = min(local_region.shape[1], x2-candidate_x1), min(local_region.shape[0], y2-candidate_y1)
    table_local[ly1:ly2, lx1:lx2] = 1

    dist_map = cv2.distanceTransform(1-table_local, cv2.DIST_L2, 5)
    weights = np.exp(-dist_map/(max(expand_w, expand_h)*0.3))
    dining_local = ((local_region * weights) > 0.4).astype(np.uint8)
    dining_local = cv2.morphologyEx(dining_local, cv2.MORPH_CLOSE, np.ones((3,3)))
    dining_local[ly1:ly2, lx1:lx2] = 1  # 强制包含局部餐桌区域

    # === 5. 生成最终掩码 ===
    final_mask = np.zeros_like(walkable)
    final_mask[candidate_y1:candidate_y2, candidate_x1:candidate_x2] = dining_local
    final_mask = final_mask & connected_mask  # 与连通域取交集
    
    # === 强制包含餐桌全局区域 ===
    final_mask[y1:y2, x1:x2] = 1
    
    # === 连通域验证 ===
    _, final_labels = cv2.connectedComponents(final_mask)
    if (final_label := final_labels[table_center[1], table_center[0]]) > 0:
        final_mask = (final_labels == final_label).astype(np.uint8)
    else:
        final_mask[y1:y2, x1:x2] = 1  # 保底措施
        _, final_labels = cv2.connectedComponents(final_mask)
        final_mask = (final_labels == final_labels[table_center[1], table_center[0]]).astype(np.uint8)

    # === 可视化 ===
    vis = cv2.cvtColor(image_gray, cv2.COLOR_GRAY2BGR)
    # contours, _ = cv2.findContours(connected_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    # cv2.drawContours(vis, contours, -1, (255,0,0), 1)
    
    overlay = vis.copy()
    overlay[np.where(final_mask)] = [0,255,0]
    vis = cv2.addWeighted(vis, 0.7, overlay, 0.3, 0)
    
    cv2.rectangle(vis, (x1,y1), (x2,y2), (0,0,255), 1)
    cv2.imwrite(output_path, vis)

    return final_mask, vis

def generate_dining_candidate_masks(image_path, table_yolo_label, output_dir="dining_candidates"):
    '''
    生成餐厅候选区域的分割掩码，保存为训练数据格式
    '''
    import os
    os.makedirs(output_dir, exist_ok=True)

    # 获取图像文件名（不含扩展名）
    image_name = os.path.splitext(os.path.basename(image_path))[0]

    print(f"正在处理图像: {image_name}")

    # 方法3: 部分连通域方法
    try:
        dining_mask3, rect_candidate, vis3 = extract_diningroom_with_guaranteed_table(
            image_path, table_yolo_label,
            os.path.join(output_dir, f"{image_name}_partial_vis.png")
        )

        # 保存掩码
        cv2.imwrite(os.path.join(output_dir, f"{image_name}_partial_mask.png"), dining_mask3 * 255)
        cv2.imwrite(os.path.join(output_dir, f"{image_name}_rect_candidate.png"), rect_candidate * 255)

        print(f"  ✓ 部分连通域方法: 最终掩码面积 = {np.sum(dining_mask3)} 像素")
        print(f"  ✓ 矩形候选区域面积 = {np.sum(rect_candidate)} 像素")

    except Exception as e:
        print(f"  ✗ 部分连通域方法失败: {e}")

    print(f"所有结果已保存到: {output_dir}")


# === 示例调用 ===
if __name__ == "__main__":
    img_path = "/home/<USER>/panpan/code/ultralytics-main/datasets/test1/9176a0d5-shunzao_10499.png"
    dining_table_label = (5, 0.4201824401368301, 0.7314709236031927, 0.14025085518814137, 0.1630558722919042)

    # img_path = "/home/<USER>/panpan/code/ultralytics-main/datasets/test3/7abe895f-shunzao_20277.png"
    # dining_table_label = (5, 0.396694214876033,0.36409550045913686,0.07713498622589533,0.10743801652892565)


    # 生成餐厅候选区域的分割掩码
    print("=== 生成餐厅候选区域分割掩码 ===")
    generate_dining_candidate_masks(
        image_path=img_path,
        table_yolo_label=dining_table_label,
        output_dir="dining_candidates"
    )

