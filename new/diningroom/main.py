import os
import cv2
from pathlib import Path

# === 引入你已有的推理函数 ===
from diningroom import generate_dining_candidate_masks  # 如果在同一个文件中则不用导入

def batch_extract_diningroom(image_dir, label_dir, output_dir):
    image_dir = Path(image_dir)
    label_dir = Path(label_dir)
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    image_paths = sorted(image_dir.glob("*.png"))  # 可改为jpg等

    for idx, image_path in enumerate(image_paths):
        label_path = label_dir / (image_path.stem + ".txt")
        if not label_path.exists():
            print(f"[!] 标签缺失: {label_path}")
            continue

        with open(label_path, 'r') as f:
            lines = f.readlines()

        for i, line in enumerate(lines):
            parts = line.strip().split()
            if len(parts) != 5:
                continue

            cls_id, cx, cy, bw, bh = map(float, parts)
            if int(cls_id) != 5:
                continue  # 只处理dining_table

            try:
                # output_path = output_dir / f"{image_path.stem}_diningroom_from_table_{i}.png"
                
                generate_dining_candidate_masks(
                    image_path=str(image_path),
                    table_yolo_label=(cls_id, cx, cy, bw, bh),
                    output_dir=str(output_dir)
                )
            except Exception as e:
                print(f"[✗] 错误处理图像 {image_path.stem} 标签#{i}: {e}")
                continue

    print("[✔] 批处理完成")

# === 示例调用 ===
if __name__ == "__main__":
    IMAGE_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0617/images/val"
    LABEL_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0617/labels/val"
    OUTPUT_DIR = "diningroom_output"

    batch_extract_diningroom(IMAGE_DIR, LABEL_DIR, OUTPUT_DIR)
