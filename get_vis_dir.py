import os
import shutil

# 源文件夹和目标文件夹路径
output_dir = 'output1'
target_dir = 'target'

# 如果目标文件夹不存在，则创建
os.makedirs(target_dir, exist_ok=True)

# 遍历 output 目录下的所有子目录和文件
for root, dirs, files in os.walk(output_dir):
    for file in files:
        if file.endswith('final_result.png'):
            src_path = os.path.join(root, file)
            dst_path = os.path.join(target_dir, file)

            # 若文件重名，可选择重命名
            if os.path.exists(dst_path):
                base, ext = os.path.splitext(file)
                i = 1
                while os.path.exists(os.path.join(target_dir, f"{base}_{i}{ext}")):
                    i += 1
                dst_path = os.path.join(target_dir, f"{base}_{i}{ext}")

            shutil.copy2(src_path, dst_path)
            print(f"Copied: {src_path} -> {dst_path}")
