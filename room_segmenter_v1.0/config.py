# 配置参数和常量
import cv2

class Config:
    # 类别名称映射
    CLASS_NAMES = {
        0: "bed_grounded",
        1: "bed_highleg",
        2: "sofa_grounded",
        3: "sofa_highleg",
        4: "door",
        5: "dining_table_set"
    }

    # 家具到房间类型的映射
    ROOM_TYPE_MAP = {
        0: "bedroom",   # bed_grounded -> 卧室
        1: "bedroom",   # bed_highleg -> 卧室
        2: "livingroom",# sofa_grounded -> 客厅
        3: "livingroom",# sofa_highleg -> 客厅
        5: "diningroom", # dining_table_set -> 餐厅
        9: "unknow"  ## unkonw一定要放在最后一个
    }

    # 房间类型颜色映射
    ROOM_COLOR_MAP = {
        "bedroom": (128, 128, 255),    # 浅蓝色
        "livingroom": (128, 255, 128), # 浅绿色
        "diningroom": (255, 128, 128), # 浅红色
        "unknow": (255, 192, 0)       # 橙色
    }

    # 家具颜色映射
    OBJECT_COLOR_MAP = {
        "bed_grounded": (160, 160, 255),
        "bed_highleg": (100, 100, 255),
        "sofa_grounded": (160, 255, 160),
        "sofa_highleg": (100, 255, 100),
        "door": (255, 0, 255),          # 紫色
        "dining_table_set": (0, 128, 255)# 蓝色
    }

    # 其他配置
    FONT = cv2.FONT_HERSHEY_TRIPLEX
    AREA_THRESH = 100
    OUTPUT_DIR = "output"

    # 房间分类配置
    DETECTION_WINDOW_SIZE = 1  # 检测窗口大小
    LARGE_DINING_ROOM_THRESHOLD = 5000  # 大面积餐厅阈值

    # 基于类别名称的ID获取方法
    @classmethod
    def get_class_id_by_name(cls, name):
        """根据类别名称获取ID"""
        for class_id, class_name in cls.CLASS_NAMES.items():
            if class_name == name:
                return class_id
        return None

    # 语义化的类别ID配置
    @property
    def DOOR_CLASS_ID(self):
        return self.get_class_id_by_name("door")

    @property
    def DINING_TABLE_CLASS_ID(self):
        return self.get_class_id_by_name("dining_table_set")

    @property
    def SOFA_GROUNDED_CLASS_ID(self):
        return self.get_class_id_by_name("sofa_grounded")

    @property
    def SOFA_HIGHLEG_CLASS_ID(self):
        return self.get_class_id_by_name("sofa_highleg")
    


    # 可视化配置
    # 图像混合权重
    IMAGE_BLEND_ALPHA = 0.5  # 原图权重
    COLOR_BLEND_ALPHA = 0.7  # 颜色权重

    # 文本配置
    TEXT_OFFSET_X = 10  # 文本X轴偏移量
    ROOM_LABEL_FONT_SIZE = 1.5  # 房间标签字体大小
    TEXT_COLOR = (0, 0, 0)  # 文本颜色
    TEXT_THICKNESS = 2  # 文本厚度

    # 检测框配置
    BOX_THICKNESS = 2  # 检测框厚度
    CENTER_POINT_RADIUS = 3  # 中心点半径
    CENTER_POINT_COLOR = (0, 0, 255)  # 中心点颜色
    CENTER_POINT_THICKNESS = -1  # 中心点填充

    # 置信度文本配置
    CONFIDENCE_FONT_SIZE = 0.3  # 置信度字体大小
    CONFIDENCE_TEXT_THICKNESS = 1  # 置信度文本厚度
    CONFIDENCE_TEXT_OFFSET = 5  # 置信度文本偏移量

    # 图例配置
    LEGEND_HEIGHT = 100  # 图例高度
    LEGEND_FONT_SIZE = 0.45  # 图例字体大小
    LEGEND_Y_OFFSET_2 = 30  # 第二行Y轴偏移
    LEGEND_Y_OFFSET_3 = 60  # 第三行Y轴偏移
    LEGEND_Y_OFFSET_4 = 90  # 第四行Y轴偏移
    LEGEND_X_STEP = 150  # X轴步长
    LEGEND_RECT_X_OFFSET = 10  # 矩形X轴偏移
    LEGEND_RECT_WIDTH = 20  # 矩形宽度
    LEGEND_RECT_HEIGHT = 20  # 矩形高度
    LEGEND_TEXT_X_OFFSET = 25  # 文本X轴偏移
    LEGEND_TEXT_Y_OFFSET = -15  # 文本Y轴偏移

    # 最终结果图像配置
    SEPARATOR_LINE_COLOR = (255, 255, 255)  # 分隔线颜色
    SEPARATOR_LINE_THICKNESS = 2  # 分隔线厚度
    FINAL_IMAGE_BACKGROUND_COLOR = 255  # 最终图像背景颜色

    # 底部文字参数
    BOTTOM_TEXT_HEIGHT = 20  # 底部文字区域高度
    BOTTOM_TEXT_FONT_SIZE = 0.6  # 底部文字大小
    BOTTOM_TEXT_COLOR = (0, 0, 0)  # 黑色文字
    BOTTOM_TEXT_THICKNESS = 2  # 文字粗细

    INTER_RATIO = 0.2