import os
from pathlib import Path

def ensure_dir(dir_path):
    """确保目录存在，不存在则创建"""
    os.makedirs(dir_path, exist_ok=True)
    return dir_path

def get_image_files(dir_path):
    """获取目录中的所有图像文件"""
    return sorted(
        f for f in os.listdir(dir_path) 
        if f.lower().endswith((".jpg", ".png", ".jpeg"))
    )

def get_label_path(image_path, label_dir):
    """根据图像路径获取对应的标签路径"""
    return os.path.join(label_dir, Path(image_path).with_suffix(".txt").name)