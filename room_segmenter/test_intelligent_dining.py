#!/usr/bin/env python3
"""
测试智能餐厅区域检测功能
"""
import os
import sys
import cv2
import numpy as np
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import Detector
from processors import RegionProcessor
from visualizer import ResultVisualizer
from utils import ensure_dir
from config import Config

def test_single_image(image_path, label_path, model_path, output_dir):
    """测试单张图像的智能餐厅区域检测"""
    
    # 初始化组件
    detector = Detector(model_path)
    processor = RegionProcessor()
    visualizer = ResultVisualizer()
    
    # 加载图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"❌ 无法加载图像: {image_path}")
        return False
        
    h, w = image.shape[:2]
    img_name = Path(image_path).stem
    
    print(f"🔍 处理图像: {img_name}")
    
    # 加载标签和检测结果
    boxes_gt, classes_gt = detector.load_yolo_labels(label_path, w, h)
    result = detector.predict(image_path)
    
    if not result:
        print(f"⚠️ 没有检测结果: {image_path}")
        return False
        
    boxes, classes, confidence_scores = detector.get_boxes_and_classes(result)
    
    # 检查是否同时包含餐桌和沙发
    has_dining_table = Config().get_class_id_by_name("dining_table_set") in classes
    has_sofa = (Config().get_class_id_by_name("sofa_grounded") in classes or 
                Config().get_class_id_by_name("sofa_highleg") in classes)
    
    if not (has_dining_table and has_sofa):
        print(f"⚠️ 图像不包含餐桌和沙发，跳过: {img_name}")
        return False
    
    print(f"✅ 图像包含餐桌和沙发，开始智能分割")
    
    # 处理图像并获取区域
    merged_labels, region_areas = processor.process_binary_image(image, boxes, classes)
    
    # 分类房间
    room_type_for_label = processor.classify_rooms(merged_labels, boxes, classes, region_areas)
    
    print(f"📊 初始房间分类: {room_type_for_label}")
    
    # 细化同时包含餐桌和沙发的区域（智能餐厅区域检测）
    refined_labels, refined_room_types = processor.refine_mixed_dining_living_rooms(
        merged_labels, boxes, classes, room_type_for_label
    )
    
    print(f"🎯 智能分割后房间分类: {refined_room_types}")
    
    # 合并同类区域
    final_labels, final_room_types = processor.bridge_merge_labels(
        refined_labels, refined_room_types
    )
    
    print(f"🔗 最终房间分类: {final_room_types}")
    
    # 可视化结果
    vis_image, _ = visualizer.draw_rooms(image, final_labels, final_room_types, boxes, classes)
    boxed_image = visualizer.draw_boxes(image, boxes, classes, confidence_scores)
    gt_image = visualizer.draw_boxes(image, boxes_gt, classes_gt)
    
    # 创建并保存最终结果
    final_image = visualizer.create_final_result(vis_image, boxed_image, gt_image)
    output_path = os.path.join(output_dir, f"{img_name}_intelligent_dining_test.png")
    cv2.imwrite(output_path, final_image)
    
    print(f"💾 结果已保存到: {output_path}")
    return True

def main():
    """主函数"""
    # 配置路径
    IMAGE_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0708/images/val"
    LABEL_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0708/labels/val"
    MODEL_PATH = "/home/<USER>/panpan/code/ultralytics-main/runs/detect/train2/weights/best.pt"
    OUTPUT_DIR = "test_output"
    
    # 确保输出目录存在
    ensure_dir(OUTPUT_DIR)
    
    # 检查路径是否存在
    if not os.path.exists(IMAGE_DIR):
        print(f"❌ 图像目录不存在: {IMAGE_DIR}")
        return
    
    if not os.path.exists(MODEL_PATH):
        print(f"❌ 模型文件不存在: {MODEL_PATH}")
        return
    
    # 获取前几张图像进行测试
    image_files = [f for f in os.listdir(IMAGE_DIR) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    
    if not image_files:
        print(f"❌ 在目录中没有找到图像文件: {IMAGE_DIR}")
        return
    
    print(f"🚀 开始测试智能餐厅区域检测功能")
    print(f"📁 图像目录: {IMAGE_DIR}")
    print(f"🏷️ 标签目录: {LABEL_DIR}")
    print(f"🤖 模型路径: {MODEL_PATH}")
    print(f"📤 输出目录: {OUTPUT_DIR}")
    
    # 测试前5张图像
    test_count = 0
    success_count = 0
    
    for img_file in image_files[:5]:  # 只测试前5张
        img_path = os.path.join(IMAGE_DIR, img_file)
        label_file = img_file.replace('.jpg', '.txt').replace('.jpeg', '.txt').replace('.png', '.txt')
        label_path = os.path.join(LABEL_DIR, label_file)
        
        if not os.path.exists(label_path):
            print(f"⚠️ 标签文件不存在: {label_file}")
            continue
        
        test_count += 1
        print(f"\n{'='*50}")
        print(f"测试 {test_count}: {img_file}")
        print(f"{'='*50}")
        
        if test_single_image(img_path, label_path, MODEL_PATH, OUTPUT_DIR):
            success_count += 1
    
    print(f"\n{'='*50}")
    print(f"🎉 测试完成!")
    print(f"📊 总测试数: {test_count}")
    print(f"✅ 成功数: {success_count}")
    print(f"❌ 失败数: {test_count - success_count}")
    print(f"📁 结果保存在: {OUTPUT_DIR}")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
