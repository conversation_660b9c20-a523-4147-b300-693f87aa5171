#!/usr/bin/env python3
"""
测试智能边界检测功能
"""
import numpy as np
import cv2
import matplotlib.pyplot as plt
from processors import RegionProcessor

def create_test_mask():
    """创建一个测试用的连通域mask"""
    # 创建一个200x200的测试图像
    mask = np.zeros((200, 200), dtype=bool)
    
    # 创建一个L形的连通域
    mask[50:150, 50:180] = True  # 水平部分
    mask[50:180, 50:100] = True  # 垂直部分
    
    # 添加一些小的噪声点（应该被忽略）
    mask[80, 45] = False  # 左边的小缺口
    mask[81, 45] = False
    mask[120, 185] = False  # 右边的小缺口
    mask[121, 185] = False
    
    return mask

def test_boundary_detection():
    """测试边界检测功能"""
    # 创建测试数据
    mask = create_test_mask()
    processor = RegionProcessor()
    
    # 定义餐桌位置（在L形区域的中心部分）
    center_x, center_y = 75, 100
    table_w, table_h = 20, 15
    
    print(f"测试餐桌位置: 中心({center_x}, {center_y}), 尺寸({table_w}x{table_h})")
    
    # 计算边界距离
    distances = processor._calculate_boundary_distances(
        mask, center_x, center_y, table_w, table_h
    )
    
    print(f"检测到的边界距离: {distances}")
    
    # 计算智能扩展区域
    exp_x1, exp_y1, exp_x2, exp_y2 = processor._calculate_smart_expansion(
        center_x, center_y, table_w, table_h, distances, mask.shape[1], mask.shape[0]
    )
    
    print(f"扩展区域: ({exp_x1}, {exp_y1}) 到 ({exp_x2}, {exp_y2})")
    
    # 找到最长距离的边
    max_distance = max(distances.values())
    max_distance_sides = [side for side, dist in distances.items() if dist == max_distance]
    print(f"最长距离: {max_distance}, 对应边: {max_distance_sides}")
    
    # 可视化结果
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # 原始连通域
    axes[0].imshow(mask, cmap='gray')
    axes[0].set_title('原始连通域')
    
    # 餐桌位置
    table_x1 = center_x - table_w // 2
    table_x2 = center_x + table_w // 2
    table_y1 = center_y - table_h // 2
    table_y2 = center_y + table_h // 2
    
    axes[0].add_patch(plt.Rectangle((table_x1, table_y1), table_w, table_h, 
                                   fill=False, edgecolor='red', linewidth=2))
    axes[0].plot(center_x, center_y, 'ro', markersize=5)
    
    # 边界检测可视化
    axes[1].imshow(mask, cmap='gray')
    axes[1].set_title('边界检测')
    
    # 绘制检测线
    sample_ys = [center_y - table_h // 4, center_y, center_y + table_h // 4]
    sample_xs = [center_x - table_w // 4, center_x, center_x + table_w // 4]
    
    # 左边检测线
    for y in sample_ys:
        if 0 <= y < mask.shape[0]:
            axes[1].plot([table_x1, table_x1 - distances['left']], [y, y], 'b-', linewidth=2)
    
    # 右边检测线
    for y in sample_ys:
        if 0 <= y < mask.shape[0]:
            axes[1].plot([table_x2, table_x2 + distances['right']], [y, y], 'g-', linewidth=2)
    
    # 上边检测线
    for x in sample_xs:
        if 0 <= x < mask.shape[1]:
            axes[1].plot([x, x], [table_y1, table_y1 - distances['top']], 'r-', linewidth=2)
    
    # 下边检测线
    for x in sample_xs:
        if 0 <= x < mask.shape[1]:
            axes[1].plot([x, x], [table_y2, table_y2 + distances['bottom']], 'm-', linewidth=2)
    
    # 最终扩展区域
    axes[2].imshow(mask, cmap='gray')
    axes[2].set_title('智能扩展区域')
    
    # 绘制扩展区域
    expanded_w = exp_x2 - exp_x1
    expanded_h = exp_y2 - exp_y1
    axes[2].add_patch(plt.Rectangle((exp_x1, exp_y1), expanded_w, expanded_h, 
                                   fill=False, edgecolor='yellow', linewidth=3))
    
    # 绘制原餐桌区域
    axes[2].add_patch(plt.Rectangle((table_x1, table_y1), table_w, table_h, 
                                   fill=False, edgecolor='red', linewidth=2))
    
    # 标注哪些边被延伸了
    if 'left' not in max_distance_sides:
        axes[2].annotate('延伸', xy=(exp_x1, center_y), xytext=(exp_x1-20, center_y),
                        arrowprops=dict(arrowstyle='->', color='blue'))
    else:
        axes[2].annotate('不延伸', xy=(table_x1, center_y), xytext=(table_x1-30, center_y),
                        arrowprops=dict(arrowstyle='->', color='red'))
    
    if 'right' not in max_distance_sides:
        axes[2].annotate('延伸', xy=(exp_x2, center_y), xytext=(exp_x2+20, center_y),
                        arrowprops=dict(arrowstyle='->', color='blue'))
    else:
        axes[2].annotate('不延伸', xy=(table_x2, center_y), xytext=(table_x2+30, center_y),
                        arrowprops=dict(arrowstyle='->', color='red'))
    
    plt.tight_layout()
    plt.savefig('boundary_detection_test.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    return distances, (exp_x1, exp_y1, exp_x2, exp_y2)

if __name__ == "__main__":
    print("🧪 开始测试智能边界检测功能")
    distances, expansion = test_boundary_detection()
    print("✅ 测试完成，结果已保存为 boundary_detection_test.png")
