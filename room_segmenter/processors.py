import cv2
import numpy as np
from scipy.ndimage import binary_fill_holes, binary_dilation
from config import Config

class RegionProcessor:
    """区域处理类，负责连通域分析和合并"""
    
    def __init__(self, area_thresh=Config.AREA_THRESH):
        self.area_thresh = area_thresh
        self.config = Config()
    
    def bridge_merge_labels(self, labels_im, room_type_for_label, kernel_size=5, iterations=3, overlap_thresh=20):
        """通过膨胀合并同类房间的连通域"""
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
        merged_labels = labels_im.copy()

        # 获取所有非背景标签
        labels = [l for l in np.unique(labels_im) if l != 0]

        # 记录掩码和膨胀掩码
        masks = {l: (merged_labels == l) for l in labels}
        dilated_masks = {
            l: cv2.dilate(masks[l].astype(np.uint8), kernel, iterations=iterations).astype(bool) 
            for l in labels
        }

        # 并查集数据结构实现
        parent = {l: l for l in labels}

        def find(x):
            """查找根节点"""
            while parent[x] != x:
                parent[x] = parent[parent[x]]
                x = parent[x]
            return x

        def union(a, b):
            """合并两个集合"""
            pa, pb = find(a), find(b)
            if pa != pb:
                parent[pb] = pa

        # 两两比较重叠且同类房间的连通域合并
        for i, l1 in enumerate(labels):
            for l2 in labels[i+1:]:
                if room_type_for_label.get(l1) != room_type_for_label.get(l2):
                    continue
                overlap = np.sum(dilated_masks[l1] & masks[l2])
                if overlap > overlap_thresh:
                    union(l1, l2)

        # 重新标记连通域
        label_map = {}
        new_label = 1
        new_room_type_for_label = {}
        new_labels_im = np.zeros_like(labels_im)

        for l in labels:
            root = find(l)
            if root not in label_map:
                label_map[root] = new_label
                new_room_type_for_label[new_label] = room_type_for_label.get(root, "unknow")
                new_label += 1
            label_map[l] = label_map[root]

        for l in labels:
            new_labels_im[merged_labels == l] = label_map[l]

        return new_labels_im, new_room_type_for_label

    def merge_regions(self, labels_im, num_labels):
        """合并相邻的小区域到大区域中"""
        # 构建每个连通区域的掩码和面积信息
        region_masks = {}
        region_areas = {}

        for i in range(1, num_labels):
            mask = (labels_im == i)
            area = np.sum(mask)
            region_masks[i] = mask
            region_areas[i] = area

        # 从大区域向小区域尝试合并
        merged_labels = labels_im.copy()
        # 按面积从大到小排序
        label_list = sorted(region_areas.items(), key=lambda x: x[1], reverse=True)  

        for big_label, big_area in label_list:
            big_mask = region_masks[big_label]
            # 向外膨胀一圈，避免紧贴边界
            big_mask_dilated = binary_dilation(big_mask, iterations=5)  

            for small_label, small_area in label_list:
                if small_label == big_label or small_area == 0:
                    continue
                small_mask = region_masks[small_label]

                # 如果小区域大部分像素都在大区域膨胀范围内，则归并
                overlap = big_mask_dilated & small_mask
                if np.sum(overlap) > Config.INTER_RATIO * np.sum(small_mask):  # 可调阈值：80%
                    merged_labels[merged_labels == small_label] = big_label  # 合并
                    region_masks[big_label] = (merged_labels == big_label)  # 更新大区域mask
                    region_areas[big_label] += region_areas[small_label]
                    region_areas[small_label] = 0  # 被合并掉

        return merged_labels, region_areas

    def process_binary_image(self, image, boxes, classes):
        """处理二值图像用于连通域分析"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, 240, 255, cv2.THRESH_BINARY)

        # 在二值图像上绘制家具区域
        for (x1, y1, x2, y2), cls in zip(boxes, classes):
            if cls == self.config.DOOR_CLASS_ID:  # 门 - 设置为背景
                cv2.rectangle(binary, (x1, y1), (x2, y2 + 1), 0, -1)
            else:  # 家具 - 设置为前景
                cv2.rectangle(binary, (x1, y1), (x2, y2), 255, -1)

        # 连通域分析
        num_labels, labels_im = cv2.connectedComponents(binary)
        return self.merge_regions(labels_im, num_labels)
    

    def classify_rooms(self, labels_im, boxes, classes, region_areas):
        """分类房间区域"""
        h, w = labels_im.shape
        room_type_for_label = {}
        max_region_label = max(region_areas, key=region_areas.get)
        max_region_area = region_areas[max_region_label]

        for i in range(1, np.max(labels_im)+1):
            if region_areas.get(i, 0) < self.config.AREA_THRESH:
                continue
                
            mask = (labels_im == i)
            present_classes = set()
            
            # 检测区域内的家具类别
            for (x1, y1, x2, y2), cls in zip(boxes, classes):
                if cls == self.config.DOOR_CLASS_ID:  # 跳过门
                    continue
                cx = (x1 + x2) // 2
                cy = (y1 + y2) // 2
                win = self.config.DETECTION_WINDOW_SIZE
                in_region = False
                for dx in range(-win, win + 1):
                    for dy in range(-win, win + 1):
                        nx, ny = cx + dx, cy + dy
                        if 0 <= nx < w and 0 <= ny < h and mask[ny, nx]:
                            in_region = True
                            break
                    if in_region:
                        present_classes.add(cls)
            
            # 根据家具类别确定房间类型
            if (self.config.DINING_TABLE_CLASS_ID in present_classes and
                (self.config.SOFA_GROUNDED_CLASS_ID in present_classes or
                 self.config.SOFA_HIGHLEG_CLASS_ID in present_classes)):
                # 同时有餐桌和沙发的区域，主要设为客厅
                # 后续会在refine_mixed_dining_living_rooms中将餐桌椅box区域分割出来作为餐厅
                room_type = self.config.ROOM_TYPE_MAP[self.config.SOFA_GROUNDED_CLASS_ID]  # 设为客厅
            else:
                room_type = list(self.config.ROOM_TYPE_MAP.values())[-1]
                for cls in present_classes:
                    if cls in self.config.ROOM_TYPE_MAP:
                        room_type = self.config.ROOM_TYPE_MAP[cls]
                        break

            # 特殊处理: 大面积餐厅视为客厅
            if (i == max_region_label and room_type == self.config.ROOM_TYPE_MAP[self.config.DINING_TABLE_CLASS_ID] and
                max_region_area > self.config.LARGE_DINING_ROOM_THRESHOLD):
                room_type = self.config.ROOM_TYPE_MAP[self.config.SOFA_GROUNDED_CLASS_ID]

            room_type_for_label[i] = room_type

        return room_type_for_label

    def refine_mixed_dining_living_rooms(self, labels_im, boxes, classes, room_type_for_label):
        """
        细化同时包含餐桌和沙发的区域
        将餐桌椅box所在区域划分成diningroom，其余还是客厅
        """
        h, w = labels_im.shape
        refined_labels = labels_im.copy()
        refined_room_types = room_type_for_label.copy()

        # 找到所有被标记为客厅但实际同时包含餐桌和沙发的区域
        for region_label, room_type in room_type_for_label.items():
            if room_type != self.config.ROOM_TYPE_MAP[self.config.SOFA_GROUNDED_CLASS_ID]:
                continue

            # 检查这个区域是否同时包含餐桌和沙发
            mask = (labels_im == region_label)
            present_classes = set()
            dining_boxes = []  # 餐桌box
            sofa_boxes = []    # 沙发box

            # 收集区域内的家具box
            for (x1, y1, x2, y2), cls in zip(boxes, classes):
                if cls == self.config.DOOR_CLASS_ID:  # 跳过门
                    continue
                cx = (x1 + x2) // 2
                cy = (y1 + y2) // 2
                win = self.config.DETECTION_WINDOW_SIZE
                in_region = False
                for dx in range(-win, win + 1):
                    for dy in range(-win, win + 1):
                        nx, ny = cx + dx, cy + dy
                        if 0 <= nx < w and 0 <= ny < h and mask[ny, nx]:
                            in_region = True
                            break
                    if in_region:
                        present_classes.add(cls)
                        if cls == self.config.DINING_TABLE_CLASS_ID:
                            dining_boxes.append((x1, y1, x2, y2))
                        elif cls in [self.config.SOFA_GROUNDED_CLASS_ID, self.config.SOFA_HIGHLEG_CLASS_ID]:
                            sofa_boxes.append((x1, y1, x2, y2))

            # 过滤掉与沙发重叠度过高的餐桌椅（可能是误检）
            if dining_boxes and sofa_boxes:
                dining_boxes = self._filter_overlapping_dining_tables(dining_boxes, sofa_boxes)

            if (self.config.BED_GROUNDED_CLASS_ID in present_classes or
                self.config.BED_HIGHLEG_CLASS_ID in present_classes) and \
            (self.config.SOFA_HIGHLEG_CLASS_ID in present_classes or self.config.SOFA_GROUNDED_CLASS_ID in present_classes):
                room_type = self.config.ROOM_TYPE_MAP[self.config.SOFA_GROUNDED_CLASS_ID] ## # 如果同时包含床和沙发，有可能床误检，设置为客厅

            # 如果同时包含餐桌和沙发，且过滤后还有有效的餐桌椅，需要细分区域
            if (dining_boxes and  # 确保过滤后还有餐桌椅
                self.config.DINING_TABLE_CLASS_ID in present_classes and
                (self.config.SOFA_GROUNDED_CLASS_ID in present_classes or
                 self.config.SOFA_HIGHLEG_CLASS_ID in present_classes)):

                # 智能地围绕餐桌椅找到餐厅区域
                dining_mask = self._find_intelligent_dining_area(
                    mask, dining_boxes, h, w
                )

                if np.any(dining_mask):
                    # 为餐桌区域分配新的标签
                    max_label = np.max(refined_labels)
                    new_dining_label = max_label + 1

                    # 更新标签图
                    refined_labels[dining_mask] = new_dining_label

                    # 更新房间类型映射
                    refined_room_types[new_dining_label] = self.config.ROOM_TYPE_MAP[self.config.DINING_TABLE_CLASS_ID]
                    refined_room_types[region_label] = self.config.ROOM_TYPE_MAP[self.config.SOFA_GROUNDED_CLASS_ID]  # 原区域改为客厅

        return refined_labels, refined_room_types

    def _find_intelligent_dining_area(self, region_mask, dining_boxes, h, w):
        """
        智能地围绕餐桌椅找到餐厅区域
        使用多种策略来确定合理的餐厅边界
        """
        import cv2
        from scipy import ndimage

        dining_mask = np.zeros_like(region_mask, dtype=bool)

        for x1, y1, x2, y2 in dining_boxes:
            # 1. 基础餐桌区域
            table_mask = np.zeros_like(region_mask, dtype=bool)
            table_mask[y1:y2, x1:x2] = True
            table_mask = table_mask & region_mask

            # 2. 计算餐桌的基本参数
            center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
            table_w, table_h = x2 - x1, y2 - y1

            # 3. 智能四边延伸：检测到连通域边界的距离
            # 计算餐桌四边到连通域边界的距离
            distances = self._calculate_boundary_distances(
                region_mask, center_x, center_y, table_w, table_h
            )

            # 根据边界距离智能确定扩展区域
            exp_x1, exp_y1, exp_x2, exp_y2 = self._calculate_smart_expansion(
                center_x, center_y, table_w, table_h, distances, w, h
            )

            # 5. 使用形态学操作优化区域形状
            initial_mask = np.zeros_like(region_mask, dtype=bool)
            initial_mask[exp_y1:exp_y2, exp_x1:exp_x2] = True
            initial_mask = initial_mask & region_mask

            # 6. 应用形态学闭运算来填补小的空洞
            kernel_size = max(3, min(table_w, table_h) // 10)
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
            refined_mask = cv2.morphologyEx(initial_mask.astype(np.uint8), cv2.MORPH_CLOSE, kernel)
            refined_mask = refined_mask.astype(bool)

            # 7. 使用连通域分析，选择包含餐桌的最大连通域
            labeled_mask, num_features = ndimage.label(refined_mask)
            if num_features > 0:
                # 找到包含餐桌中心的连通域
                table_center_label = labeled_mask[center_y, center_x] if labeled_mask[center_y, center_x] > 0 else None

                if table_center_label is not None:
                    # 选择包含餐桌中心的连通域
                    final_mask = (labeled_mask == table_center_label)
                else:
                    # 如果餐桌中心不在任何连通域内，选择最大的连通域
                    component_sizes = ndimage.sum(refined_mask, labeled_mask, range(1, num_features + 1))
                    largest_component = np.argmax(component_sizes) + 1
                    final_mask = (labeled_mask == largest_component)

                # 8. 最终优化：确保餐桌区域被包含
                final_mask = final_mask | table_mask

                # 9. 边界平滑处理
                kernel_smooth = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
                final_mask = cv2.morphologyEx(final_mask.astype(np.uint8), cv2.MORPH_OPEN, kernel_smooth)
                final_mask = final_mask.astype(bool)

                dining_mask = dining_mask | final_mask
            else:
                # 如果形态学操作失败，回退到简单扩展
                dining_mask[exp_y1:exp_y2, exp_x1:exp_x2] = True

        # 最终确保结果在原区域内
        dining_mask = dining_mask & region_mask
        return dining_mask

    def _calculate_boundary_distances(self, region_mask, center_x, center_y, table_w, table_h):
        """
        计算餐桌四边到连通域边界的距离
        使用多条线检测边界，忽略单个点导致的边界，只考虑大范围的连通域边界
        返回: {'left': distance, 'right': distance, 'top': distance, 'bottom': distance}
        """
        h, w = region_mask.shape
        distances = {}

        # 设置边界检测的容忍度
        boundary_tolerance = max(3, min(table_w, table_h) // 8)

        # 计算左边距离 - 使用多条垂直线
        left_distances = []
        sample_ys = [center_y - table_h // 4, center_y, center_y + table_h // 4]
        for sample_y in sample_ys:
            if 0 <= sample_y < h:
                distance = self._calculate_single_direction_distance(
                    region_mask, center_x - table_w // 2, sample_y, -1, 0, boundary_tolerance, w, h
                )
                left_distances.append(distance)
        distances['left'] = min(left_distances) if left_distances else 0

        # 计算右边距离 - 使用多条垂直线
        right_distances = []
        for sample_y in sample_ys:
            if 0 <= sample_y < h:
                distance = self._calculate_single_direction_distance(
                    region_mask, center_x + table_w // 2, sample_y, 1, 0, boundary_tolerance, w, h
                )
                right_distances.append(distance)
        distances['right'] = min(right_distances) if right_distances else 0

        # 计算上边距离 - 使用多条水平线
        top_distances = []
        sample_xs = [center_x - table_w // 4, center_x, center_x + table_w // 4]
        for sample_x in sample_xs:
            if 0 <= sample_x < w:
                distance = self._calculate_single_direction_distance(
                    region_mask, sample_x, center_y - table_h // 2, 0, -1, boundary_tolerance, w, h
                )
                top_distances.append(distance)
        distances['top'] = min(top_distances) if top_distances else 0

        # 计算下边距离 - 使用多条水平线
        bottom_distances = []
        for sample_x in sample_xs:
            if 0 <= sample_x < w:
                distance = self._calculate_single_direction_distance(
                    region_mask, sample_x, center_y + table_h // 2, 0, 1, boundary_tolerance, w, h
                )
                bottom_distances.append(distance)
        distances['bottom'] = min(bottom_distances) if bottom_distances else 0

        return distances

    def _calculate_single_direction_distance(self, region_mask, start_x, start_y, dx, dy,
                                           boundary_tolerance, w, h):
        """
        计算单个方向的边界距离
        """
        distance = 0
        consecutive_false = 0
        x, y = start_x, start_y

        while True:
            x += dx
            y += dy

            # 检查是否超出图像边界
            if x < 0 or x >= w or y < 0 or y >= h:
                break

            if region_mask[y, x]:
                distance += 1
                consecutive_false = 0  # 重置连续False计数
            else:
                consecutive_false += 1
                if consecutive_false >= boundary_tolerance:
                    # 遇到大范围边界，停止延伸
                    distance -= consecutive_false - 1  # 减去已计算的False像素
                    break
                # 如果只是小的间隙，继续延伸
                distance += 1

        return max(0, distance)

    def _filter_overlapping_dining_tables(self, dining_boxes, sofa_boxes):
        """
        过滤掉与沙发重叠度过高的餐桌椅（可能是误检）
        """
        # IoU阈值，超过此值认为是误检
        iou_threshold = 0.6  # 如果餐桌椅与沙发的IoU超过30%，认为是误检

        filtered_dining_boxes = []

        for dining_box in dining_boxes:
            is_valid_dining = True

            # 检查该餐桌椅与所有沙发的IoU
            for sofa_box in sofa_boxes:
                iou = self._calculate_box_iou(dining_box, sofa_box)

                if iou > iou_threshold:
                    print(f"⚠️ 餐桌椅 {dining_box} 与沙发 {sofa_box} 重叠度过高 (IoU={iou:.3f})，舍弃该餐桌椅")
                    is_valid_dining = False
                    break

            if is_valid_dining:
                filtered_dining_boxes.append(dining_box)

        return filtered_dining_boxes

    def _calculate_box_iou(self, box1, box2):
        """
        计算两个边界框的IoU (Intersection over Union)
        box格式: (x1, y1, x2, y2)
        """
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2

        # 计算交集区域
        x1_inter = max(x1_1, x1_2)
        y1_inter = max(y1_1, y1_2)
        x2_inter = min(x2_1, x2_2)
        y2_inter = min(y2_1, y2_2)

        # 如果没有交集
        if x1_inter >= x2_inter or y1_inter >= y2_inter:
            return 0.0

        # 计算交集面积
        intersection_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)

        # 计算两个框的面积
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)

        # 计算并集面积
        union_area = area1 + area2 - intersection_area

        # 计算IoU
        if union_area == 0:
            return 0.0

        return intersection_area / union_area

    def _calculate_smart_expansion(self, center_x, center_y, table_w, table_h,
                                 distances, w, h):
        """
        根据边界距离智能确定扩展区域
        最长距离边不延伸，其他边延伸到连通域边界
        """
        # 找到最长距离的边
        max_distance = max(distances.values())
        max_distance_sides = [side for side, dist in distances.items() if dist == max_distance]

        # 计算餐桌的原始边界
        table_x1 = center_x - table_w // 2
        table_x2 = center_x + table_w // 2
        table_y1 = center_y - table_h // 2
        table_y2 = center_y + table_h // 2

        # 初始化扩展后的边界（默认延伸到连通域边界）
        exp_x1 = table_x1 - distances['left']  # 左边延伸到边界
        exp_x2 = table_x2 + distances['right']  # 右边延伸到边界
        exp_y1 = table_y1 - distances['top']    # 上边延伸到边界
        exp_y2 = table_y2 + distances['bottom'] # 下边延伸到边界

        # 最长距离边不延伸（保持餐桌原边界）
        if 'left' in max_distance_sides:
            exp_x1 = table_x1  # 左边不延伸
        if 'right' in max_distance_sides:
            exp_x2 = table_x2  # 右边不延伸
        if 'top' in max_distance_sides:
            exp_y1 = table_y1  # 上边不延伸
        if 'bottom' in max_distance_sides:
            exp_y2 = table_y2  # 下边不延伸

        # 确保边界在图像范围内
        exp_x1 = max(0, exp_x1)
        exp_x2 = min(w, exp_x2)
        exp_y1 = max(0, exp_y1)
        exp_y2 = min(h, exp_y2)

        return exp_x1, exp_y1, exp_x2, exp_y2