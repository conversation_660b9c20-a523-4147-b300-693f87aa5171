
from ultralytics import YOLO

class Detector:
    """YOLO检测器封装类"""
    
    def __init__(self, model_path):
        self.model = YOLO(model_path)
    
    def predict(self, image_path, conf=0.2):
        """执行预测"""
        results = self.model.predict(image_path, save=False, show=False, conf=conf)
        return results[0] if results else None
    
    @staticmethod
    def get_boxes_and_classes(result):
        """从YOLO结果中提取边界框、类别和置信度"""
        boxes, classes, confs = [], [], []
        for box in result.boxes:
            xyxy = box.xyxy[0].cpu().numpy().astype(int)
            cls_id = int(box.cls[0])
            conf = box.conf[0].cpu().item()
            x1, y1, x2, y2 = xyxy
            boxes.append([x1, y1, x2, y2])
            classes.append(cls_id)
            confs.append(conf)
        return boxes, classes, confs

    @staticmethod
    def load_yolo_labels(label_file, img_w, img_h):
        """加载YOLO格式的标签文件"""
        boxes, classes = [], []
        with open(label_file, 'r') as f:
            for line in f:
                cls, cx, cy, w, h = map(float, line.strip().split())
                cls = int(cls)
                x1 = int((cx - w / 2) * img_w)
                y1 = int((cy - h / 2) * img_h)
                x2 = int((cx + w / 2) * img_w)
                y2 = int((cy + h / 2) * img_h)
                boxes.append([x1, y1, x2, y2])
                classes.append(cls)
        return boxes, classes