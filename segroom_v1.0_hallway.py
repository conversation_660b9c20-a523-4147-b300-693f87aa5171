# import cv2
# import numpy as np
# import os
# from pathlib import Path
# from scipy.ndimage import binary_fill_holes, binary_dilation

# class RoomSegmenter:
#     def __init__(self,
#                  image_dir,
#                  label_dir,
#                  output_dir="output",
#                  area_thresh=100):

#         self.image_dir = image_dir
#         self.label_dir = label_dir
#         self.output_dir = output_dir
#         self.area_thresh = area_thresh

#         self.class_names = {
#             0: "bed_grounded",
#             1: "bed_highleg",
#             2: "sofa_grounded",
#             3: "sofa_highleg",
#             4: "hallway",
#             5: "door",
#             6: "dining_table_set"
#         }

#         self.room_type_map = {
#             0: "bedroom",
#             1: "bedroom",
#             2: "livingroom",
#             3: "livingroom",
#             4: "hallway",
#             6: "diningroom"
#         }

#         self.room_color_map = {
#             "bedroom": (128, 128, 255),
#             "livingroom": (128, 255, 128),
#             "diningroom": (255, 128, 128),
#             "hallway": (255, 255, 0),
#             "unknow": (255, 192, 0)
#         }

#         self.object_color_map = {
#             "bed_grounded": (160, 160, 255),
#             "bed_highleg": (100, 100, 255),
#             "sofa_grounded": (160, 255, 160),
#             "sofa_highleg": (100, 255, 100),
#             "hallway": (255, 255, 0),
#             "door": (255, 0, 255),
#             "dining_table_set": (0, 128, 255)
#         }

#         self.font = cv2.FONT_HERSHEY_SIMPLEX

#     def load_yolo_labels(self, label_file, img_w, img_h):
#         boxes, classes = [], []
#         with open(label_file, 'r') as f:
#             for line in f:
#                 cls, cx, cy, w, h = map(float, line.strip().split())
#                 cls = int(cls)
#                 x1 = int((cx - w / 2) * img_w)
#                 y1 = int((cy - h / 2) * img_h)
#                 x2 = int((cx + w / 2) * img_w)
#                 y2 = int((cy + h / 2) * img_h)
#                 boxes.append([x1, y1, x2, y2])
#                 classes.append(cls)
#         return boxes, classes
    
#     def bridge_merge_labels(self, labels_im, room_type_for_label, kernel_size=5, iterations=3, overlap_thresh=20):
#         """
#         膨胀同类房间的连通域掩码，若膨胀后有重叠则合并，减少被门分割导致的断裂。

#         labels_im: int label image
#         room_type_for_label: dict {label: room_type string}
#         kernel_size: 膨胀核大小
#         iterations: 膨胀次数
#         overlap_thresh: 重叠像素阈值，超过则合并

#         返回合并后的labels_im和更新的room_type_for_label
#         """
#         kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
#         merged_labels = labels_im.copy()

#         # 获取所有标签（排除0背景）
#         labels = [l for l in np.unique(labels_im) if l != 0]

#         # 记录掩码和膨胀掩码
#         masks = {l: (merged_labels == l) for l in labels}
#         dilated_masks = {l: cv2.dilate(masks[l].astype(np.uint8), kernel, iterations=iterations).astype(bool) for l in labels}

#         # 合并映射，初始自己指向自己
#         parent = {l: l for l in labels}

#         def find(x):
#             while parent[x] != x:
#                 parent[x] = parent[parent[x]]
#                 x = parent[x]
#             return x

#         def union(a, b):
#             pa, pb = find(a), find(b)
#             if pa != pb:
#                 parent[pb] = pa

#         # 两两比较重叠且同类房间的连通域合并
#         for i, l1 in enumerate(labels):
#             for l2 in labels[i+1:]:
#                 if room_type_for_label.get(l1) != room_type_for_label.get(l2):
#                     continue
#                 overlap = np.sum(dilated_masks[l1] & masks[l2])
#                 if overlap > overlap_thresh:
#                     union(l1, l2)

#         # 重新标记连通域
#         label_map = {}
#         new_label = 1
#         new_room_type_for_label = {}
#         new_labels_im = np.zeros_like(labels_im)

#         for l in labels:
#             root = find(l)
#             if root not in label_map:
#                 label_map[root] = new_label
#                 new_room_type_for_label[new_label] = room_type_for_label.get(root, "unknow")
#                 new_label += 1
#             label_map[l] = label_map[root]

#         for l in labels:
#             new_labels_im[merged_labels == l] = label_map[l]

#         return new_labels_im, new_room_type_for_label


#     def merge_regions(self, labels_im, num_labels):
#         # 构建每个连通区域的掩码和面积信息
#         region_masks = {}
#         region_areas = {}

#         for i in range(1, num_labels):
#             mask = (labels_im == i)
#             area = np.sum(mask)
#             region_masks[i] = mask
#             region_areas[i] = area

#         # 从大区域向小区域尝试合并
#         merged_labels = labels_im.copy()
#         label_list = sorted(region_areas.items(), key=lambda x: x[1], reverse=True)  # 按面积从大到小排序

#         for big_label, big_area in label_list:
#             big_mask = region_masks[big_label]
#             big_mask_dilated = binary_dilation(big_mask, iterations=5)  # 向外膨胀一圈，避免紧贴边界

#             for small_label, small_area in label_list:
#                 if small_label == big_label or small_area == 0:
#                     continue
#                 small_mask = region_masks[small_label]

#                 # 如果小区域大部分像素都在大区域膨胀范围内，则归并
#                 overlap = big_mask_dilated & small_mask
#                 if np.sum(overlap) > 0.2 * np.sum(small_mask):  # 可调阈值：80%
#                     merged_labels[merged_labels == small_label] = big_label  # 合并
#                     region_masks[big_label] = (merged_labels == big_label)  # 更新大区域mask
#                     region_areas[big_label] += region_areas[small_label]
#                     region_areas[small_label] = 0  # 被合并掉


#         return merged_labels, region_areas

#     def classify_and_visualize(self, image, labels_im, boxes, classes, region_areas, save_dir, img_name):
#         h, w = labels_im.shape
#         vis_image = image.copy()
#         color_mask = np.zeros_like(image)
#         room_idx = 0
#         room_type_for_label = {}

#         # 找最大连通域label
#         max_region_label = max(region_areas, key=region_areas.get)
#         max_region_area = region_areas[max_region_label]

#         for i in range(1, np.max(labels_im)+1):
#             if region_areas.get(i, 0) < self.area_thresh:
#                 continue
#             mask = (labels_im == i)
#             present_classes = set()
#             for (x1, y1, x2, y2), cls in zip(boxes, classes):
#                 if cls == 5:
#                     continue
#                 cx = (x1 + x2) // 2
#                 cy = (y1 + y2) // 2
#                 win = 1
#                 in_region = False
#                 for dx in range(-win, win + 1):
#                     for dy in range(-win, win + 1):
#                         nx, ny = cx + dx, cy + dy
#                         if 0 <= nx < w and 0 <= ny < h and mask[ny, nx]:
#                             in_region = True
#                             break
#                     if in_region:
#                         present_classes.add(cls)

#             if 6 in present_classes and (2 in present_classes or 3 in present_classes):
#                 room_type = "livingroom"
#             else:
#                 room_type = "unknow"
#                 for cls in present_classes:
#                     if cls in self.room_type_map:
#                         room_type = self.room_type_map[cls]
#                         break

#             if i == max_region_label and room_type == "diningroom" and max_region_area > 5000:
#                 room_type = "livingroom"

            
#             mask_filled = binary_fill_holes(mask)
#             mask_uint8 = (mask_filled * 255).astype(np.uint8)
#             contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
#             filled_mask = np.zeros_like(mask_uint8)
#             cv2.drawContours(filled_mask, contours, -1, 255, thickness=-1)
#             filled_mask_bool = filled_mask.astype(bool)

#             color = self.room_color_map[room_type]
#             vis_image[filled_mask_bool] = cv2.addWeighted(vis_image, 0.5, np.full_like(image, color), 0.7, 0)[filled_mask_bool]
#             color_mask[filled_mask_bool] = color

#             ys, xs = np.where(filled_mask_bool)
#             if len(xs) > 0 and len(ys) > 0:
#                 cx, cy = int(np.mean(xs)), int(np.mean(ys))
#                 cv2.putText(vis_image, room_type, (cx - 40, cy), self.font, 0.5, (0, 0, 0), 2)

#             # # 保存mask
#             cv2.imwrite(f"{save_dir}/{room_type}_{room_idx}.png", (mask_filled.astype(np.uint8) * 255))
#             room_idx += 1

#         # 绘制门框
#         for (x1, y1, x2, y2), cls in zip(boxes, classes):
#             if cls == 5:
#                 cv2.rectangle(vis_image, (x1, y1), (x2, y2), (255, 0, 255), 2)

#         # 绘制目标框和中心点
#         boxed_image = image.copy()
#         for (x1, y1, x2, y2), cls in zip(boxes, classes):
#             label = self.class_names.get(cls, str(cls))
#             color = self.object_color_map.get(label, (0, 255, 255))
#             cv2.rectangle(boxed_image, (x1, y1), (x2, y2), color, 2)
#             if label != "door":
#                 cx = (x1 + x2) // 2
#                 cy = (y1 + y2) // 2
#                 cv2.circle(boxed_image, (cx, cy), 3, (0, 0, 255), -1)
#         # 新增：基于类别合并连通域，减少断裂
#         labels_im, room_type_for_label = self.bridge_merge_labels(labels_im, room_type_for_label)

#         # 重新计算区域面积等（可复用merge_regions逻辑）
#         _, region_areas = self.merge_regions(labels_im, np.max(labels_im) + 1)
        
#         # 拼接结果图
#         vis_h, vis_w = vis_image.shape[:2]
#         legend_height = 100
#         final_image = np.ones((vis_h + legend_height, vis_w * 2, 3), dtype=np.uint8) * 255
#         final_image[legend_height:, :vis_w] = vis_image
#         final_image[legend_height:, vis_w:] = boxed_image
#         cv2.line(final_image, (vis_w, legend_height), (vis_w, vis_h + legend_height), (255, 255, 255), 2)

#         # 添加图例
#         self._draw_legend(final_image, legend_height)

#         # 保存结果
#         cv2.imwrite(f"{save_dir}/overlay.png", vis_image)
#         cv2.imwrite(f"{save_dir}/mask_color.png", color_mask)
#         cv2.imwrite(f"{save_dir}/boxed.png", boxed_image)
#         cv2.imwrite(f"{save_dir}/{img_name}_final_result.png", final_image)

#     def _draw_legend(self, final_image, legend_height):
#         legend_font = 0.45
#         y_offset_2 = 20
#         y_offset_3 = 50
#         y_offset_4 = 80
#         x_step = 150

#         for i, label in enumerate(["bed_grounded", "bed_highleg"]):
#             color = self.object_color_map[label]
#             x = 10 + i * x_step
#             cv2.rectangle(final_image, (x, y_offset_2 - 15), (x + 20, y_offset_2 + 5), color, -1)
#             cv2.putText(final_image, label, (x + 25, y_offset_2 + 2), self.font, legend_font, (0, 0, 0), 1)

#         for i, label in enumerate(["sofa_grounded", "sofa_highleg"]):
#             color = self.object_color_map[label]
#             x = 10 + i * x_step
#             cv2.rectangle(final_image, (x, y_offset_3 - 15), (x + 20, y_offset_3 + 5), color, -1)
#             cv2.putText(final_image, label, (x + 25, y_offset_3 + 2), self.font, legend_font, (0, 0, 0), 1)

#         for i, label in enumerate(["door", "dining_table_set"]):
#             color = self.object_color_map[label]
#             x = 10 + i * x_step
#             cv2.rectangle(final_image, (x, y_offset_4 - 15), (x + 20, y_offset_4 + 5), color, -1)
#             cv2.putText(final_image, label, (x + 25, y_offset_4 + 2), self.font, legend_font, (0, 0, 0), 1)

#     def process_image(self, image_path, label_path):
#         img_name = Path(image_path).stem
#         image = cv2.imread(image_path)
#         if image is None:
#             print(f"⚠️ Failed to load image: {image_path}")
#             return
#         gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
#         h, w = gray.shape

#         boxes, classes = self.load_yolo_labels(label_path, w, h)

#         _, binary = cv2.threshold(gray, 240, 255, cv2.THRESH_BINARY)

#         for (x1, y1, x2, y2), cls in zip(boxes, classes):
#             if cls == 5:  # door  
#                 y2_fixed = min(y2 + 1, h - 1) 
#                 cv2.rectangle(binary, (x1, y1), (x2, y2_fixed), 0, -1)
#             elif cls == 4:  # 走廊 hallway
#                 cv2.rectangle(binary, (x1, y1), (x2, y2 + 1), 128, -1)
#             elif cls in [0, 1, 2, 3, 6]:
#                 cv2.rectangle(binary, (x1, y1), (x2, y2), 255, -1)

        

#         num_labels, labels_im = cv2.connectedComponents(binary)
#         merged_labels, region_areas = self.merge_regions(labels_im, num_labels)

#         save_dir = os.path.join(self.output_dir, img_name)
#         os.makedirs(save_dir, exist_ok=True)
#         self.classify_and_visualize(image, merged_labels, boxes, classes, region_areas, save_dir, img_name)
#         print(f"✅ Processed {img_name}, Saved to {save_dir}")

#     def batch_process(self):
#         os.makedirs(self.output_dir, exist_ok=True)
#         for img_file in sorted(os.listdir(self.image_dir)):
#             if not img_file.lower().endswith((".jpg", ".png")):
#                 continue
#             img_path = os.path.join(self.image_dir, img_file)
#             label_path = os.path.join(self.label_dir, Path(img_file).with_suffix(".txt"))
#             if not os.path.exists(label_path):
#                 print(f"⚠️ Label not found for {img_file}")
#                 continue
#             self.process_image(img_path, label_path)


# if __name__ == "__main__":
#     IMAGE_DIR = "/home/<USER>/panpan/code/segmentRoom/data/images/"
#     LABEL_DIR = "/home/<USER>/panpan/code/segmentRoom/data/labels/"
#     OUTPUT_DIR = "output_halway"
#     AREA_THRESH = 100

#     segmenter = RoomSegmenter(IMAGE_DIR, LABEL_DIR, OUTPUT_DIR, AREA_THRESH)
#     segmenter.batch_process()


import cv2
import numpy as np
import os
from pathlib import Path
from scipy.ndimage import binary_fill_holes, binary_dilation

class RoomSegmenter:
    def __init__(self,
                 image_dir,
                 label_dir,
                 output_dir="output",
                 area_thresh=100):

        self.image_dir = image_dir
        self.label_dir = label_dir
        self.output_dir = output_dir
        self.area_thresh = area_thresh

        self.class_names = {
            0: "bed_grounded",
            1: "bed_highleg",
            2: "sofa_grounded",
            3: "sofa_highleg",
            4: "hallway",
            5: "door",
            6: "dining_table_set"
        }

        self.room_type_map = {
            0: "bedroom",
            1: "bedroom",
            2: "livingroom",
            3: "livingroom",
            4: "hallway",
            6: "diningroom"
        }

        self.room_color_map = {
            "bedroom": (128, 128, 255),
            "livingroom": (128, 255, 128),
            "diningroom": (0, 128, 255),
            "hallway": (23, 255, 255),
            "unknow": (255, 192, 0)
        }

        self.object_color_map = {
            "bed_grounded": (160, 160, 255),
            "bed_highleg": (100, 100, 255),
            "sofa_grounded": (160, 255, 160),
            "sofa_highleg": (100, 255, 100),
            "hallway": (23, 255, 255),
            "door": (255, 0, 255),
            "dining_table_set": (0, 128, 255)
        }

        self.font = cv2.FONT_HERSHEY_SIMPLEX

    def load_yolo_labels(self, label_file, img_w, img_h):
        boxes, classes = [], []
        with open(label_file, 'r') as f:
            for line in f:
                cls, cx, cy, w, h = map(float, line.strip().split())
                cls = int(cls)
                x1 = int((cx - w / 2) * img_w)
                y1 = int((cy - h / 2) * img_h)
                x2 = int((cx + w / 2) * img_w)
                y2 = int((cy + h / 2) * img_h)
                boxes.append([x1, y1, x2, y2])
                classes.append(cls)
        return boxes, classes
    
    def bridge_merge_labels(self, labels_im, room_type_for_label, kernel_size=5, iterations=3, overlap_thresh=20):
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
        merged_labels = labels_im.copy()

        labels = [l for l in np.unique(labels_im) if l != 0]
        masks = {l: (merged_labels == l) for l in labels}
        dilated_masks = {l: cv2.dilate(masks[l].astype(np.uint8), kernel, iterations=iterations).astype(bool) for l in labels}

        parent = {l: l for l in labels}

        def find(x):
            while parent[x] != x:
                parent[x] = parent[parent[x]]
                x = parent[x]
            return x

        def union(a, b):
            pa, pb = find(a), find(b)
            if pa != pb:
                parent[pb] = pa

        for i, l1 in enumerate(labels):
            for l2 in labels[i+1:]:
                if room_type_for_label.get(l1) != room_type_for_label.get(l2):
                    continue
                overlap = np.sum(dilated_masks[l1] & masks[l2])
                if overlap > overlap_thresh:
                    union(l1, l2)

        label_map = {}
        new_label = 1
        new_room_type_for_label = {}
        new_labels_im = np.zeros_like(labels_im)

        for l in labels:
            root = find(l)
            if root not in label_map:
                label_map[root] = new_label
                new_room_type_for_label[new_label] = room_type_for_label.get(root, "unknow")
                new_label += 1
            label_map[l] = label_map[root]

        for l in labels:
            new_labels_im[merged_labels == l] = label_map[l]

        return new_labels_im, new_room_type_for_label

    def merge_regions(self, labels_im, num_labels):
        region_masks = {}
        region_areas = {}

        for i in range(1, num_labels):
            mask = (labels_im == i)
            area = np.sum(mask)
            region_masks[i] = mask
            region_areas[i] = area

        merged_labels = labels_im.copy()
        label_list = sorted(region_areas.items(), key=lambda x: x[1], reverse=True)

        for big_label, big_area in label_list:
            big_mask = region_masks[big_label]
            big_mask_dilated = binary_dilation(big_mask, iterations=5)

            for small_label, small_area in label_list:
                if small_label == big_label or small_area == 0:
                    continue
                small_mask = region_masks[small_label]
                overlap = big_mask_dilated & small_mask
                if np.sum(overlap) > 0.2 * np.sum(small_mask):
                    merged_labels[merged_labels == small_label] = big_label
                    region_masks[big_label] = (merged_labels == big_label)
                    region_areas[big_label] += region_areas[small_label]
                    region_areas[small_label] = 0

        return merged_labels, region_areas

    def classify_and_visualize(self, image, labels_im, boxes, classes, region_areas, save_dir, img_name):
        h, w = labels_im.shape
        vis_image = image.copy()
        color_mask = np.zeros_like(image)
        room_idx = 0
        room_type_for_label = {}

        max_region_label = max(region_areas, key=region_areas.get)
        max_region_area = region_areas[max_region_label]

        # 首先处理走廊区域（标签值>=1000）
        for i in range(1001, np.max(labels_im)+1):
            if i not in region_areas or region_areas.get(i, 0) < self.area_thresh:
                continue
            mask = (labels_im == i)
            room_type = "hallway"
            room_type_for_label[i] = room_type
            
            mask_filled = binary_fill_holes(mask)
            mask_uint8 = (mask_filled * 255).astype(np.uint8)
            contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            filled_mask = np.zeros_like(mask_uint8)
            cv2.drawContours(filled_mask, contours, -1, 255, thickness=-1)
            filled_mask_bool = filled_mask.astype(bool)

            color = self.room_color_map[room_type]
            # 走廊特殊样式：黄色半透明+红色轮廓
            vis_image[filled_mask_bool] = cv2.addWeighted(vis_image, 0.3, np.full_like(image, color), 0.7, 0)[filled_mask_bool]
            # cv2.drawContours(vis_image, contours, -1, (0, 0, 255), 2)
            color_mask[filled_mask_bool] = color

            ys, xs = np.where(filled_mask_bool)
            if len(xs) > 0 and len(ys) > 0:
                cx, cy = int(np.mean(xs)), int(np.mean(ys))
                cv2.putText(vis_image, room_type, (cx - 40, cy), self.font, 0.5, (0, 0, 0), 2)

            cv2.imwrite(f"{save_dir}/{room_type}_{room_idx}.png", (mask_filled.astype(np.uint8) * 255))
            room_idx += 1

        # 然后处理其他房间区域
        for i in range(1, 1000):
            if i not in region_areas or region_areas.get(i, 0) < self.area_thresh:
                continue
            mask = (labels_im == i)
            present_classes = set()
            for (x1, y1, x2, y2), cls in zip(boxes, classes):
                if cls == 5:
                    continue
                cx = (x1 + x2) // 2
                cy = (y1 + y2) // 2
                win = 1
                in_region = False
                for dx in range(-win, win + 1):
                    for dy in range(-win, win + 1):
                        nx, ny = cx + dx, cy + dy
                        if 0 <= nx < w and 0 <= ny < h and mask[ny, nx]:
                            in_region = True
                            break
                    if in_region:
                        break
                if in_region:
                    present_classes.add(cls)

            if 6 in present_classes and (2 in present_classes or 3 in present_classes):
                room_type = "livingroom"
            else:
                room_type = "unknow"
                for cls in present_classes:
                    if cls in self.room_type_map:
                        room_type = self.room_type_map[cls]
                        break

            if i == max_region_label and room_type == "diningroom" and max_region_area > 5000:
                room_type = "livingroom"

            room_type_for_label[i] = room_type
            mask_filled = binary_fill_holes(mask)
            mask_uint8 = (mask_filled * 255).astype(np.uint8)
            contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            filled_mask = np.zeros_like(mask_uint8)
            cv2.drawContours(filled_mask, contours, -1, 255, thickness=-1)
            filled_mask_bool = filled_mask.astype(bool)

            color = self.room_color_map[room_type]
            vis_image[filled_mask_bool] = cv2.addWeighted(vis_image, 0.5, np.full_like(image, color), 0.5, 0)[filled_mask_bool]
            color_mask[filled_mask_bool] = color

            ys, xs = np.where(filled_mask_bool)
            if len(xs) > 0 and len(ys) > 0:
                cx, cy = int(np.mean(xs)), int(np.mean(ys))
                cv2.putText(vis_image, room_type, (cx - 40, cy), self.font, 0.5, (0, 0, 0), 2)

            cv2.imwrite(f"{save_dir}/{room_type}_{room_idx}.png", (mask_filled.astype(np.uint8) * 255))
            room_idx += 1

        # 绘制门框
        for (x1, y1, x2, y2), cls in zip(boxes, classes):
            if cls == 5:
                cv2.rectangle(vis_image, (x1, y1), (x2, y2), (255, 0, 255), 2)

        # 绘制目标框和中心点
        boxed_image = image.copy()
        for (x1, y1, x2, y2), cls in zip(boxes, classes):
            label = self.class_names.get(cls, str(cls))
            color = self.object_color_map.get(label, (0, 255, 255))
            cv2.rectangle(boxed_image, (x1, y1), (x2, y2), color, 2)
            if label != "door":
                cx = (x1 + x2) // 2
                cy = (y1 + y2) // 2
                cv2.circle(boxed_image, (cx, cy), 3, (0, 0, 255), -1)

        # # 合并同类区域
        labels_im, room_type_for_label = self.bridge_merge_labels(labels_im, room_type_for_label)
        _, region_areas = self.merge_regions(labels_im, np.max(labels_im) + 1)
        
        # 拼接结果图
        vis_h, vis_w = vis_image.shape[:2]
        legend_height = 100
        final_image = np.ones((vis_h + legend_height, vis_w * 2, 3), dtype=np.uint8) * 255
        final_image[legend_height:, :vis_w] = vis_image
        final_image[legend_height:, vis_w:] = boxed_image
        cv2.line(final_image, (vis_w, legend_height), (vis_w, vis_h + legend_height), (255, 255, 255), 2)

        # 添加图例
        self._draw_legend(final_image, legend_height)

        # 保存结果
        cv2.imwrite(f"{save_dir}/overlay.png", vis_image)
        cv2.imwrite(f"{save_dir}/mask_color.png", color_mask)
        cv2.imwrite(f"{save_dir}/boxed.png", boxed_image)
        cv2.imwrite(f"{save_dir}/{img_name}_final_result.png", final_image)

    def _draw_legend(self, final_image, legend_height):
        legend_font = 0.45
        y_offset_2 = 20
        y_offset_3 = 50
        y_offset_4 = 80
        x_step = 150

        for i, label in enumerate(["bed_grounded", "bed_highleg"]):
            color = self.object_color_map[label]
            x = 10 + i * x_step
            cv2.rectangle(final_image, (x, y_offset_2 - 15), (x + 20, y_offset_2 + 5), color, -1)
            cv2.putText(final_image, label, (x + 25, y_offset_2 + 2), self.font, legend_font, (0, 0, 0), 1)

        for i, label in enumerate(["sofa_grounded", "sofa_highleg"]):
            color = self.object_color_map[label]
            x = 10 + i * x_step
            cv2.rectangle(final_image, (x, y_offset_3 - 15), (x + 20, y_offset_3 + 5), color, -1)
            cv2.putText(final_image, label, (x + 25, y_offset_3 + 2), self.font, legend_font, (0, 0, 0), 1)

        for i, label in enumerate(["door", "dining_table_set"]):
            color = self.object_color_map[label]
            x = 10 + i * x_step
            cv2.rectangle(final_image, (x, y_offset_4 - 15), (x + 20, y_offset_4 + 5), color, -1)
            cv2.putText(final_image, label, (x + 25, y_offset_4 + 2), self.font, legend_font, (0, 0, 0), 1)

    def process_image(self, image_path, label_path):
        img_name = Path(image_path).stem
        image = cv2.imread(image_path)
        if image is None:
            print(f"⚠️ Failed to load image: {image_path}")
            return
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        h, w = gray.shape

        boxes, classes = self.load_yolo_labels(label_path, w, h)

        # 获取原始连通域（在填充前）
        _, binary_original = cv2.threshold(gray, 240, 255, cv2.THRESH_BINARY)
        num_labels_original, labels_original = cv2.connectedComponents(binary_original)

        # 创建填充后的二值图像
        _, binary = cv2.threshold(gray, 240, 255, cv2.THRESH_BINARY)
        
        # 预处理：不同类别用不同灰度值标记
        for (x1, y1, x2, y2), cls in zip(boxes, classes):
            if cls == 5:  # 门：黑色(0)
                y2_fixed = min(y2 + 1, h - 1) 
                cv2.rectangle(binary, (x1, y1), (x2, y2_fixed), 0, -1)
            elif cls == 4:  # 走廊：灰色(128)
                cv2.rectangle(binary, (x1, y1), (x2, y2 + 1), 128, -1)
            elif cls in [0, 1, 2, 3, 6]:  # 其他家具：白色(255)
                cv2.rectangle(binary, (x1, y1), (x2, y2), 255, -1)

        # 分别处理房间和走廊的连通域
        room_mask = (binary == 255)
        hallway_mask = (binary == 128)
        
        # 房间区域连通域分析
        _, room_labels = cv2.connectedComponents(room_mask.astype(np.uint8))
        # 走廊区域连通域分析
        _, hallway_labels = cv2.connectedComponents(hallway_mask.astype(np.uint8))
        
        # 合并标签（走廊标签从1001开始）
        final_labels = room_labels.copy()
        hallway_labels[hallway_labels > 0] += 1000
        final_labels[hallway_mask] = hallway_labels[hallway_mask]
        
        # 计算区域面积并过滤小连通域
        region_areas = {}
        min_area_threshold = self.area_thresh  # 使用类初始化时设置的阈值
        
        # 先处理房间区域
        for i in range(1, np.max(room_labels)+1):
            mask = (room_labels == i)
            area = np.sum(mask)
            if area >= min_area_threshold:  # 只保留足够大的区域
                region_areas[i] = area
            else:
                final_labels[final_labels == i] = 0  # 小区域设为背景
        
        # 处理走廊区域
        for i in range(1, np.max(hallway_labels)+1):
            hallway_i = i + 1000  # 走廊标签偏移
            mask = (hallway_labels == i)
            area = np.sum(mask)
            
            # 确保走廊区域与原始连通域的交集
            hallway_label_mask = (final_labels == hallway_i)
            valid_mask = np.zeros_like(hallway_label_mask)
            
            # 找到与原始连通域的交集
            for orig_label in range(1, num_labels_original):
                orig_mask = (labels_original == orig_label)
                intersection = np.logical_and(hallway_label_mask, orig_mask)
                intersection_area = np.sum(intersection)
                
                # 只保留足够大的交集部分
                if intersection_area >= min_area_threshold:
                    valid_mask = np.logical_or(valid_mask, intersection)
            
            valid_area = np.sum(valid_mask)
            if valid_area >= min_area_threshold:
                final_labels[hallway_label_mask] = 0  # 先清空原有区域
                final_labels[valid_mask] = hallway_i  # 只保留有效部分
                region_areas[hallway_i] = valid_area  # 更新区域面积
            else:
                final_labels[hallway_label_mask] = 0  # 小区域设为背景

        save_dir = os.path.join(self.output_dir, img_name)
        os.makedirs(save_dir, exist_ok=True)
        self.classify_and_visualize(image, final_labels, boxes, classes, region_areas, save_dir, img_name)
        print(f"✅ Processed {img_name}, Saved to {save_dir}")

    def batch_process(self):
        os.makedirs(self.output_dir, exist_ok=True)
        for img_file in sorted(os.listdir(self.image_dir)):
            if not img_file.lower().endswith((".jpg", ".png")):
                continue
            img_path = os.path.join(self.image_dir, img_file)
            label_path = os.path.join(self.label_dir, Path(img_file).with_suffix(".txt"))
            if not os.path.exists(label_path):
                print(f"⚠️ Label not found for {img_file}")
                continue
            self.process_image(img_path, label_path)


if __name__ == "__main__":
    IMAGE_DIR = "/home/<USER>/panpan/code/segmentRoom/data/images/"
    LABEL_DIR = "/home/<USER>/panpan/code/segmentRoom/data/labels/"
    OUTPUT_DIR = "output_hallway"
    AREA_THRESH = 100

    segmenter = RoomSegmenter(IMAGE_DIR, LABEL_DIR, OUTPUT_DIR, AREA_THRESH)
    segmenter.batch_process()