import cv2
import numpy as np
import os
from pathlib import Path
from scipy.ndimage import binary_fill_holes, binary_dilation
from ultralytics import YOLO

class RoomSegmenter:
    """房间分割与分类工具类
    
    功能：
    - 使用YOLO模型检测室内家具
    - 基于家具位置和类型分割房间区域
    - 对房间区域进行分类（卧室、客厅、餐厅等）
    - 可视化结果
    """
    
    def __init__(self, image_dir, label_dir, model_path, output_dir="output", area_thresh=100):
        """初始化房间分割器
        
        参数:
            image_dir: 输入图像目录路径
            label_dir: 标签文件目录路径
            model_path: YOLO模型路径
            output_dir: 输出目录路径
            area_thresh: 最小区域面积阈值
        """
        self.image_dir = image_dir
        self.label_dir = label_dir
        self.output_dir = output_dir
        self.area_thresh = area_thresh
        self.model_path = model_path

        # 类别名称映射
        self.class_names = {
            0: "bed_grounded",
            1: "bed_highleg",
            2: "sofa_grounded",
            3: "sofa_highleg",
            4: "door",
            5: "dining_table_set"
        }

        # 家具到房间类型的映射
        self.room_type_map = {
            0: "bedroom",   # bed_grounded -> 卧室
            1: "bedroom",   # bed_highleg -> 卧室
            2: "livingroom",# sofa_grounded -> 客厅
            3: "livingroom",# sofa_highleg -> 客厅
            5: "diningroom" # dining_table_set -> 餐厅
        }

        # 房间类型颜色映射
        self.room_color_map = {
            "bedroom": (128, 128, 255),    # 浅蓝色
            "livingroom": (128, 255, 128), # 浅绿色
            "diningroom": (255, 128, 128), # 浅红色
            "unknow": (255, 192, 0)       # 橙色
        }

        # 家具颜色映射
        self.object_color_map = {
            "bed_grounded": (160, 160, 255),
            "bed_highleg": (100, 100, 255),
            "sofa_grounded": (160, 255, 160),
            "sofa_highleg": (100, 255, 100),
            "door": (255, 0, 255),          # 紫色
            "dining_table_set": (0, 128, 255)# 蓝色
        }

        self.font = cv2.FONT_HERSHEY_SIMPLEX

    def load_yolo_labels(self, label_file, img_w, img_h):
        """加载YOLO格式的标签文件
        
        参数:
            label_file: 标签文件路径
            img_w: 图像宽度
            img_h: 图像高度
            
        返回:
            boxes: 边界框列表 [[x1,y1,x2,y2], ...]
            classes: 类别ID列表
        """
        boxes, classes = [], []
        with open(label_file, 'r') as f:
            for line in f:
                cls, cx, cy, w, h = map(float, line.strip().split())
                cls = int(cls)
                # 将归一化坐标转换为像素坐标
                x1 = int((cx - w / 2) * img_w)
                y1 = int((cy - h / 2) * img_h)
                x2 = int((cx + w / 2) * img_w)
                y2 = int((cy + h / 2) * img_h)
                boxes.append([x1, y1, x2, y2])
                classes.append(cls)
        return boxes, classes
    
    def get_boxes_and_classes(self, result):
        """从YOLO结果中提取边界框、类别和置信度
        
        参数:
            result: YOLO检测结果
            
        返回:
            boxes: 边界框列表
            classes: 类别ID列表
            confs: 置信度列表
        """
        boxes, classes, confs = [], [], []
        for box in result.boxes:
            xyxy = box.xyxy[0].cpu().numpy().astype(int)
            cls_id = int(box.cls[0])
            conf = box.conf[0].cpu().item()
            x1, y1, x2, y2 = xyxy
            boxes.append([x1, y1, x2, y2])
            classes.append(cls_id)
            confs.append(conf)
        return boxes, classes, confs

    def bridge_merge_labels(self, labels_im, room_type_for_label, kernel_size=5, iterations=3, overlap_thresh=20):
        """通过膨胀合并同类房间的连通域
        
        参数:
            labels_im: 标签图像
            room_type_for_label: 标签到房间类型的映射
            kernel_size: 膨胀核大小
            iterations: 膨胀次数
            overlap_thresh: 合并重叠阈值
            
        返回:
            new_labels_im: 合并后的标签图像
            new_room_type_for_label: 更新后的房间类型映射
        """
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
        merged_labels = labels_im.copy()

        # 获取所有非背景标签
        labels = [l for l in np.unique(labels_im) if l != 0]

        # 记录掩码和膨胀掩码
        masks = {l: (merged_labels == l) for l in labels}
        dilated_masks = {
            l: cv2.dilate(masks[l].astype(np.uint8), kernel, iterations=iterations).astype(bool) 
            for l in labels
        }

        # 并查集数据结构实现
        parent = {l: l for l in labels}

        def find(x):
            """查找根节点"""
            while parent[x] != x:
                parent[x] = parent[parent[x]]
                x = parent[x]
            return x

        def union(a, b):
            """合并两个集合"""
            pa, pb = find(a), find(b)
            if pa != pb:
                parent[pb] = pa

        # 两两比较重叠且同类房间的连通域合并
        for i, l1 in enumerate(labels):
            for l2 in labels[i+1:]:
                if room_type_for_label.get(l1) != room_type_for_label.get(l2):
                    continue
                overlap = np.sum(dilated_masks[l1] & masks[l2])
                if overlap > overlap_thresh:
                    union(l1, l2)

        # 重新标记连通域
        label_map = {}
        new_label = 1
        new_room_type_for_label = {}
        new_labels_im = np.zeros_like(labels_im)

        for l in labels:
            root = find(l)
            if root not in label_map:
                label_map[root] = new_label
                new_room_type_for_label[new_label] = room_type_for_label.get(root, "unknow")
                new_label += 1
            label_map[l] = label_map[root]

        for l in labels:
            new_labels_im[merged_labels == l] = label_map[l]

        return new_labels_im, new_room_type_for_label

    def merge_regions(self, labels_im, num_labels):
        """合并相邻的小区域到大区域中
        
        参数:
            labels_im: 标签图像
            num_labels: 标签数量
            
        返回:
            merged_labels: 合并后的标签图像
            region_areas: 区域面积字典
        """
        # 构建每个连通区域的掩码和面积信息
        region_masks = {}
        region_areas = {}

        for i in range(1, num_labels):
            mask = (labels_im == i)
            area = np.sum(mask)
            region_masks[i] = mask
            region_areas[i] = area

        # 从大区域向小区域尝试合并
        merged_labels = labels_im.copy()
        # 按面积从大到小排序
        label_list = sorted(region_areas.items(), key=lambda x: x[1], reverse=True)  

        for big_label, big_area in label_list:
            big_mask = region_masks[big_label]
            # 向外膨胀一圈，避免紧贴边界
            big_mask_dilated = binary_dilation(big_mask, iterations=5)  

            for small_label, small_area in label_list:
                if small_label == big_label or small_area == 0:
                    continue
                small_mask = region_masks[small_label]

                # 如果小区域大部分像素都在大区域膨胀范围内，则归并
                overlap = big_mask_dilated & small_mask
                if np.sum(overlap) > 0.2 * np.sum(small_mask):  # 可调阈值：80%
                    merged_labels[merged_labels == small_label] = big_label  # 合并
                    region_masks[big_label] = (merged_labels == big_label)  # 更新大区域mask
                    region_areas[big_label] += region_areas[small_label]
                    region_areas[small_label] = 0  # 被合并掉

        return merged_labels, region_areas

    def classify_and_visualize(self, image, labels_im, boxes, classes, confidence_scores, 
                             boxes_gt, class_gt, region_areas, save_dir, img_name):
        """分类房间区域并可视化结果
        
        参数:
            image: 原始图像
            labels_im: 标签图像
            boxes: 检测框列表
            classes: 检测类别列表
            confidence_scores: 置信度列表
            boxes_gt: 真实框列表
            class_gt: 真实类别列表
            region_areas: 区域面积字典
            save_dir: 保存目录
            img_name: 图像名称
        """
        h, w = labels_im.shape
        vis_image = image.copy()
        gt_image = image.copy()
        color_mask = np.zeros_like(image)
        room_idx = 0
        room_type_for_label = {}

        # 找最大连通域label
        max_region_label = max(region_areas, key=region_areas.get)
        max_region_area = region_areas[max_region_label]

        # 对每个区域进行分类
        for i in range(1, np.max(labels_im)+1):
            if region_areas.get(i, 0) < self.area_thresh:
                continue
                
            mask = (labels_im == i)
            present_classes = set()
            
            # 检测区域内的家具类别
            for (x1, y1, x2, y2), cls in zip(boxes, classes):
                if cls == 4:  # 跳过门
                    continue
                cx = (x1 + x2) // 2
                cy = (y1 + y2) // 2
                win = 1
                in_region = False
                for dx in range(-win, win + 1):
                    for dy in range(-win, win + 1):
                        nx, ny = cx + dx, cy + dy
                        if 0 <= nx < w and 0 <= ny < h and mask[ny, nx]:
                            in_region = True
                            break
                    if in_region:
                        present_classes.add(cls)

            # 根据家具类别确定房间类型
            if 5 in present_classes and (2 in present_classes or 3 in present_classes):
                room_type = "livingroom"  # 同时有餐桌和沙发的区域视为客厅
            else:
                room_type = "unknow"
                for cls in present_classes:
                    if cls in self.room_type_map:
                        room_type = self.room_type_map[cls]
                        break

            # 特殊处理: 大面积餐厅视为客厅
            if i == max_region_label and room_type == "diningroom" and max_region_area > 5000:
                room_type = "livingroom"

            # 填充区域并绘制
            mask_filled = binary_fill_holes(mask)
            mask_uint8 = (mask_filled * 255).astype(np.uint8)
            contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            filled_mask = np.zeros_like(mask_uint8)
            cv2.drawContours(filled_mask, contours, -1, 255, thickness=-1)
            filled_mask_bool = filled_mask.astype(bool)

            # 应用半透明颜色
            color = self.room_color_map[room_type]
            vis_image[filled_mask_bool] = cv2.addWeighted(
                vis_image, 0.5, np.full_like(image, color), 0.7, 0
            )[filled_mask_bool]
            color_mask[filled_mask_bool] = color

            # 添加房间类型标签
            ys, xs = np.where(filled_mask_bool)
            if len(xs) > 0 and len(ys) > 0:
                cx, cy = int(np.mean(xs)), int(np.mean(ys))
                cv2.putText(vis_image, room_type, (cx - 40, cy), 
                           self.font, 0.5, (0, 0, 0), 2)

            room_idx += 1

        # 绘制门框
        for (x1, y1, x2, y2), cls in zip(boxes, classes):
            if cls == 4:  # 门
                cv2.rectangle(vis_image, (x1, y1), (x2, y2), (255, 0, 255), 2)
               
        # 绘制检测框和中心点
        boxed_image = image.copy()
        for (x1, y1, x2, y2), cls, conf in zip(boxes, classes, confidence_scores):
            label = self.class_names.get(cls, str(cls))
            color = self.object_color_map.get(label, (0, 255, 255))
            cv2.rectangle(boxed_image, (x1, y1), (x2, y2), color, 2)
            if label != "door":
                cx = (x1 + x2) // 2
                cy = (y1 + y2) // 2
                cv2.circle(boxed_image, (cx, cy), 3, (0, 0, 255), -1)
            
            # 添加置信度文本
            text = f"{conf:.2f}"
            (tw, th), _ = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, 0.3, 1)
            cv2.rectangle(boxed_image, (x1, y1 - th - 5), (x1 + tw, y1), color, -1)
            cv2.putText(boxed_image, text, (x1, y1 - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 0, 0), 1)
        
        # 绘制真实框
        for (x1, y1, x2, y2), cls in zip(boxes_gt, class_gt):
            label = self.class_names.get(cls, str(cls))
            color = self.object_color_map.get(label, (0, 255, 255))
            cv2.rectangle(gt_image, (x1, y1), (x2, y2), color, 2)
            if label != "door":
                cx = (x1 + x2) // 2
                cy = (y1 + y2) // 2
                cv2.circle(gt_image, (cx, cy), 3, (0, 0, 255), -1)

        # 基于类别合并连通域，减少断裂
        labels_im, room_type_for_label = self.bridge_merge_labels(labels_im, room_type_for_label)
        _, region_areas = self.merge_regions(labels_im, np.max(labels_im) + 1)
        
        # 拼接结果图
        vis_h, vis_w = vis_image.shape[:2]
        legend_height = 100
        final_image = np.ones((vis_h + legend_height, vis_w * 3, 3), dtype=np.uint8) * 255
        
        # 排列三张结果图
        final_image[legend_height:, :vis_w] = vis_image        # 分割可视化
        final_image[legend_height:, vis_w:2*vis_w] = boxed_image  # 检测结果
        final_image[legend_height:, 2*vis_w:] = gt_image       # 真实标注

        # 添加分隔线
        cv2.line(final_image, (vis_w, legend_height), (vis_w, vis_h + legend_height), 
              (255, 255, 255), 2)
        cv2.line(final_image, (2*vis_w, legend_height), (2*vis_w, vis_h + legend_height), 
              (255, 255, 255), 2)
        
        # 添加图例
        self._draw_legend(final_image, legend_height)

        # 保存最终结果
        cv2.imwrite(f"{save_dir}/{img_name}_final_result.png", final_image)

    def _draw_legend(self, image, legend_height):
        """在图像顶部绘制图例
        
        参数:
            image: 要绘制图例的图像
            legend_height: 图例区域高度
        """
        legend_font = 0.45
        y_offset_2 = 20
        y_offset_3 = 50
        y_offset_4 = 80
        x_step = 150

        # 绘制家具类别图例
        for i, label in enumerate(["bed_grounded", "bed_highleg"]):
            color = self.object_color_map[label]
            x = 10 + i * x_step
            cv2.rectangle(image, (x, y_offset_2 - 15), (x + 20, y_offset_2 + 5), color, -1)
            cv2.putText(image, label, (x + 25, y_offset_2 + 2), 
                       self.font, legend_font, (0, 0, 0), 1)

        for i, label in enumerate(["sofa_grounded", "sofa_highleg"]):
            color = self.object_color_map[label]
            x = 10 + i * x_step
            cv2.rectangle(image, (x, y_offset_3 - 15), (x + 20, y_offset_3 + 5), color, -1)
            cv2.putText(image, label, (x + 25, y_offset_3 + 2), 
                       self.font, legend_font, (0, 0, 0), 1)

        for i, label in enumerate(["door", "dining_table_set"]):
            color = self.object_color_map[label]
            x = 10 + i * x_step
            cv2.rectangle(image, (x, y_offset_4 - 15), (x + 20, y_offset_4 + 5), color, -1)
            cv2.putText(image, label, (x + 25, y_offset_4 + 2), 
                       self.font, legend_font, (0, 0, 0), 1)

    def process_image(self, image_path, result, label_path):
        """处理单张图像
        
        参数:
            image_path: 图像路径
            result: YOLO检测结果
            label_path: 标签文件路径
        """
        img_name = Path(image_path).stem
        image = cv2.imread(image_path)
        if image is None:
            print(f"⚠️ Failed to load image: {image_path}")
            return
            
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        h, w = gray.shape

        # 加载标签和检测结果
        boxes_gt, classes_gt = self.load_yolo_labels(label_path, w, h)
        boxes, classes, confidence_scores = self.get_boxes_and_classes(result)
        
        # 创建二值图像用于连通域分析
        _, binary = cv2.threshold(gray, 240, 255, cv2.THRESH_BINARY)

        # 在二值图像上绘制家具区域
        for (x1, y1, x2, y2), cls in zip(boxes, classes):
            if cls == 4:  # 门 - 设置为背景
                cv2.rectangle(binary, (x1, y1), (x2, y2 + 1), 0, -1)
            elif cls in [0, 1, 2, 3, 5]:  # 家具 - 设置为前景
                cv2.rectangle(binary, (x1, y1), (x2, y2), 255, -1)

        # 连通域分析
        num_labels, labels_im = cv2.connectedComponents(binary)
        merged_labels, region_areas = self.merge_regions(labels_im, num_labels)

        # 分类和可视化
        self.classify_and_visualize(
            image, merged_labels, boxes, classes, confidence_scores,
            boxes_gt, classes_gt, region_areas, self.output_dir, img_name
        )
        print(f"✅ Processed {img_name}, Saved to {self.output_dir}")

    def batch_process(self):
        """批量处理图像目录中的所有图像"""
        model = YOLO(self.model_path)
        os.makedirs(self.output_dir, exist_ok=True)
        
        for img_file in sorted(os.listdir(self.image_dir)):
            if not img_file.lower().endswith((".jpg", ".png")):
                continue
                
            img_path = os.path.join(self.image_dir, img_file)
            label_path = os.path.join(self.label_dir, Path(img_file).with_suffix(".txt"))
            
            if not os.path.exists(label_path):
                print(f"⚠️ Label not found for {img_file}")
                continue
                
            # 使用YOLO模型进行预测
            results = model.predict(img_path, save=False, show=False, conf=0.2)
            
            if results:  # 如果有检测结果
                result = results[0]
                self.process_image(img_path, result, label_path)


if __name__ == "__main__":
    # 配置路径参数
    IMAGE_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0708/images/val"
    LABEL_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0708/labels/val"
    OUTPUT_DIR = "output"
    AREA_THRESH = 100
    MODEL_PATH = "/home/<USER>/panpan/code/ultralytics-main/runs/detect/train2/weights/best.pt"

    # 创建并运行房间分割器
    segmenter = RoomSegmenter(IMAGE_DIR, LABEL_DIR, MODEL_PATH, OUTPUT_DIR, AREA_THRESH)
    segmenter.batch_process()