# 餐厅区域分割功能

## 概述

本次更新在 `room_segmenter` 中增加了专门的餐厅区域分割功能，能够精确识别和分割包含餐桌的区域，并将其标识为餐厅（diningroom）。

## 新增功能

### 1. 精确餐厅区域分割
- 基于 `new/diningroom/diningroom.py` 中的核心算法
- 使用连通域分析和距离变换优化餐厅边界
- 确保餐桌区域始终包含在最终的餐厅分割结果中

### 2. 智能区域处理
- 自动检测图像中的餐桌位置
- 基于餐桌尺寸动态调整候选区域
- 处理餐桌不在可通行区域的特殊情况

### 3. 与现有系统集成
- 无缝集成到现有的房间分割流程中
- 保持与其他房间类型（卧室、客厅等）的兼容性
- 支持多个餐桌的独立处理

## 文件结构

```
room_segmenter/
├── dining_processor.py      # 新增：餐厅专门处理器
├── processors.py           # 修改：集成餐厅分割功能
├── main.py                 # 修改：添加餐厅分割步骤
├── test_dining_room.py     # 新增：测试脚本
├── config.py               # 现有：配置文件（包含餐厅相关配置）
├── models.py               # 现有：YOLO检测器
├── visualizer.py           # 现有：可视化工具
└── utils.py                # 现有：工具函数
```

## 核心算法

### DiningRoomProcessor 类

#### `extract_dining_room_region(image, dining_table_box)`
- **输入**: 图像和餐桌边界框
- **输出**: 餐厅区域掩码
- **算法步骤**:
  1. 获取可通行区域连通域
  2. 基于餐桌尺寸生成候选区域
  3. 使用距离变换进行局部优化
  4. 连通域验证确保结果完整性

#### `process_dining_rooms_in_labels(image, labels_im, boxes, classes, room_type_for_label)`
- **功能**: 在已分割的房间标签中处理餐厅区域
- **特点**: 
  - 支持多个餐桌的处理
  - 智能处理区域重叠
  - 动态分配新的房间标签

## 使用方法

### 1. 基本使用
```python
from main import RoomSegmenter

# 创建房间分割器
segmenter = RoomSegmenter(IMAGE_DIR, LABEL_DIR, MODEL_PATH, OUTPUT_DIR)

# 批量处理（自动包含餐厅分割）
segmenter.batch_process()
```

### 2. 单独使用餐厅处理器
```python
from dining_processor import DiningRoomProcessor

processor = DiningRoomProcessor()
dining_mask = processor.extract_dining_room_region(image, dining_table_box)
```

### 3. 运行测试
```bash
cd room_segmenter
python test_dining_room.py
```

## 配置参数

在 `config.py` 中的相关配置：

```python
# 餐厅相关配置
DINING_TABLE_CLASS_ID = 5           # 餐桌类别ID
ROOM_TYPE_MAP[5] = "diningroom"     # 餐桌 -> 餐厅映射
ROOM_COLOR_MAP["diningroom"] = (255, 128, 128)  # 餐厅颜色（浅红色）

# 分割参数
AREA_THRESH = 100                   # 最小区域面积阈值
LARGE_DINING_ROOM_THRESHOLD = 5000  # 大面积餐厅阈值
```

## 算法特点

### 1. 鲁棒性
- 处理餐桌不在可通行区域的情况
- 提供备用分割方法
- 连通域验证确保结果完整性

### 2. 精确性
- 基于距离变换的权重计算
- 形态学操作优化边界
- 强制包含餐桌区域

### 3. 灵活性
- 支持不同尺寸的餐桌
- 动态调整候选区域
- 可配置的重叠处理阈值

## 输出结果

处理后的图像将包含：
- 精确分割的餐厅区域（浅红色标识）
- 餐桌检测框和标签
- 与其他房间类型的清晰区分
- 完整的可视化结果图像

## 测试验证

运行 `test_dining_room.py` 将生成：
- `dining_room_test_result.png`: 餐厅分割可视化结果
- `dining_room_mask.png`: 餐厅区域掩码
- `*_final_result.png`: 完整的房间分割结果

## 注意事项

1. **图像质量**: 确保输入图像清晰，餐桌边界明显
2. **标签准确性**: 餐桌检测的准确性直接影响分割效果
3. **参数调整**: 可根据具体场景调整配置参数
4. **内存使用**: 大图像处理时注意内存占用

## 故障排除

### 常见问题
1. **餐厅区域过小**: 检查 `AREA_THRESH` 设置
2. **分割不准确**: 验证餐桌检测结果
3. **区域重叠**: 调整重叠处理阈值（默认30%）

### 调试方法
- 使用测试脚本验证功能
- 检查中间结果图像
- 查看控制台输出信息
