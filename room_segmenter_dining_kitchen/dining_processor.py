import cv2
import numpy as np
from config import Config

class DiningRoomProcessor:
    """餐厅区域专门处理器，基于diningroom.py的核心分割算法"""
    
    def __init__(self):
        self.config = Config()
    
    def _calculate_adaptive_expansion(self, image, dining_table_box):
        """
        自适应计算餐厅区域扩展范围

        Args:
            image: 输入图像
            dining_table_box: 餐桌边界框 [x1, y1, x2, y2]

        Returns:
            expand_w, expand_h: 扩展的宽度和高度
        """
        h, w = image.shape[:2] if len(image.shape) == 3 else image.shape
        x1, y1, x2, y2 = dining_table_box
        table_width = x2 - x1
        table_height = y2 - y1

        # 方法1: 基于图像尺寸的自适应扩展
        # 小图像使用较大的扩展比例，大图像使用较小的扩展比例
        image_area = h * w
        if image_area < 30000:  # 小图像 (< 300k像素)
            base_factor = 1.5
        elif image_area < 80000:  # 中等图像 (300k-800k像素)
            base_factor = 1.3
        else:  # 大图像 (> 800k像素)
            base_factor = 1

        # 方法2: 基于餐桌尺寸相对于图像的比例
        table_area = table_width * table_height
        table_ratio = table_area / image_area

        # 如果餐桌相对较小，需要更大的扩展
        if table_ratio < 0.02:  # 餐桌很小
            size_factor = 2.5
        elif table_ratio < 0.03:  # 餐桌中等
            size_factor = 1.2
        else:  # 餐桌较大
            size_factor = 1

        # 方法3: 基于餐桌位置的边界约束
        # 如果餐桌靠近边界，限制扩展范围
        center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2

        # 计算到边界的距离
        dist_to_left = center_x
        dist_to_right = w - center_x
        dist_to_top = center_y
        dist_to_bottom = h - center_y

        # 基于边界距离调整扩展范围
        max_expand_w = min(dist_to_left, dist_to_right) * 1.8
        max_expand_h = min(dist_to_top, dist_to_bottom) * 1.8

        # 综合计算最终扩展范围
        expand_w = min(int(table_width * min(base_factor, size_factor)), max_expand_w)
        expand_h = min(int(table_height * min(base_factor, size_factor)), max_expand_h)

        # 确保最小扩展范围
        min_expand = max(table_width, table_height) // 2
        expand_w = max(expand_w, min_expand)
        expand_h = max(expand_h, min_expand)

        return expand_w, expand_h

    def _find_optimal_dining_region(self, image, dining_table_box):
        """
        通过迭代方法找到最优的餐厅区域

        Args:
            image: 输入图像
            dining_table_box: 餐桌边界框

        Returns:
            optimal_mask: 最优的餐厅区域掩码
        """
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        h, w = gray.shape
        x1, y1, x2, y2 = dining_table_box
        table_center = ((x1 + x2) // 2, (y1 + y2) // 2)

        # 获取可通行区域
        walkable = ((gray > 240) & (cv2.Canny(gray, 50, 150) == 0)).astype(np.uint8)
        _, labels = cv2.connectedComponents(walkable)

        table_label = labels[table_center[1], table_center[0]]
        if table_label == 0:
            return None

        connected_mask = (labels == table_label).astype(np.uint8)

        # 尝试不同的扩展范围，找到最合适的
        table_width = x2 - x1
        table_height = y2 - y1

        best_mask = None
        best_score = 0

        # 尝试多个扩展因子
        for factor in [1.2, 1.5, 1.8, 2.0, 2.5]:
            expand_w = int(table_width * factor)
            expand_h = int(table_height * factor)

            candidate_x1 = max(0, table_center[0] - expand_w // 2)
            candidate_y1 = max(0, table_center[1] - expand_h // 2)
            candidate_x2 = min(w, table_center[0] + expand_w // 2)
            candidate_y2 = min(h, table_center[1] + expand_h // 2)

            # 创建候选区域
            candidate_mask = np.zeros_like(walkable)
            candidate_mask[candidate_y1:candidate_y2, candidate_x1:candidate_x2] = 1
            candidate_mask = candidate_mask & connected_mask

            # 评估候选区域的质量
            area = np.sum(candidate_mask)
            if area < self.config.AREA_THRESH:
                continue

            # 计算紧凑度 (面积与周长的比值)
            contours, _ = cv2.findContours(candidate_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            if contours:
                perimeter = cv2.arcLength(contours[0], True)
                if perimeter > 0:
                    compactness = area / (perimeter * perimeter)

                    # 计算与餐桌的相对位置合理性
                    table_coverage = np.sum(candidate_mask[y1:y2, x1:x2]) / ((y2-y1) * (x2-x1))

                    # 综合评分
                    score = compactness * 1000 + table_coverage * 100 + area * 0.001

                    if score > best_score:
                        best_score = score
                        best_mask = candidate_mask.copy()

        return best_mask

    def _check_if_table_in_separate_region(self, image, dining_table_box, boxes, classes):
        """
        检查餐桌椅是否处于一个单独的连通域

        Args:
            image: 输入图像
            dining_table_box: 餐桌边界框
            boxes: 所有检测框
            classes: 所有类别

        Returns:
            separate_region_mask: 如果餐桌在单独连通域，返回该连通域掩码；否则返回None
        """
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        h, w = gray.shape
        x1, y1, x2, y2 = dining_table_box
        table_center = ((x1 + x2) // 2, (y1 + y2) // 2)

        # 创建二值图像用于连通域分析
        _, binary = cv2.threshold(gray, 240, 255, cv2.THRESH_BINARY)

        # 在二值图像上绘制家具区域
        for (bx1, by1, bx2, by2), cls in zip(boxes, classes):
            if cls == self.config.DOOR_CLASS_ID:  # 门 - 设置为背景
                cv2.rectangle(binary, (bx1, by1), (bx2 + 1, by2 + 1), 0, -1)
            else:  # 家具 - 设置为前景
                cv2.rectangle(binary, (bx1, by1), (bx2, by2), 255, -1)

        # 连通域分析
        _, labels = cv2.connectedComponents(binary)

        # 获取餐桌所在的连通域标签
        table_label = labels[table_center[1], table_center[0]]
        if table_label == 0:
            return None  # 餐桌不在可通行区域

        # 获取餐桌所在的连通域
        table_region_mask = (labels == table_label).astype(np.uint8)

        # 检查这个连通域的面积是否合适作为餐厅
        region_area = np.sum(table_region_mask)
        if region_area < self.config.AREA_THRESH * 3:  # 区域太小
            return None

        # 检查这个连通域内的家具类型
        dining_table_count = 0
        other_furniture_count = 0
        furniture_in_region = []

        for (bx1, by1, bx2, by2), cls in zip(boxes, classes):
            if cls == self.config.DOOR_CLASS_ID:
                continue

            # 检查家具中心是否在这个连通域内
            furniture_center = ((bx1 + bx2) // 2, (by1 + by2) // 2)
            if table_region_mask[furniture_center[1], furniture_center[0]]:
                furniture_in_region.append((cls, furniture_center))
                if cls == self.config.DINING_TABLE_CLASS_ID:
                    dining_table_count += 1
                else:
                    other_furniture_count += 1

        # 判断是否适合作为单独的餐厅区域
        # 严格条件：只有餐桌，没有其他家具（如沙发、床等）
        if dining_table_count > 0 and other_furniture_count == 0:
            print(f"发现单独的餐厅连通域，面积: {region_area} 像素，只包含餐桌家具")
            print(f"  连通域内家具: {[self.config.CLASS_NAMES.get(cls, f'class_{cls}') for cls, _ in furniture_in_region]}")
            return table_region_mask
        elif dining_table_count > 0 and other_furniture_count > 0:
            print(f"餐桌连通域包含其他家具，不适合作为单独餐厅")
            print(f"  连通域内家具: {[self.config.CLASS_NAMES.get(cls, f'class_{cls}') for cls, _ in furniture_in_region]}")

        return None

    def _filter_dining_region_by_color(self, image, dining_mask, dining_table_box):
        """
        根据颜色区域过滤餐厅区域，只保留白色区域的大部分

        Args:
            image: 输入图像
            dining_mask: 原始餐厅区域掩码
            dining_table_box: 餐桌边界框

        Returns:
            filtered_mask: 过滤后的餐厅区域掩码
        """
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        x1, y1, x2, y2 = dining_table_box
        table_center = ((x1 + x2) // 2, (y1 + y2) // 2)

        # 定义白色区域 (亮度 > 240)
        white_region = (gray > 240).astype(np.uint8)

        # 将餐厅区域与白色区域取交集
        white_dining_mask = dining_mask & white_region

        # 如果白色区域太小，返回原始掩码
        white_area = np.sum(white_dining_mask)
        total_area = np.sum(dining_mask)

        if white_area < total_area * 0.3:  # 白色区域少于30%，保持原样
            return dining_mask

        # 对白色餐厅区域进行连通域分析
        _, white_labels = cv2.connectedComponents(white_dining_mask)

        # 找到包含餐桌中心的连通域
        table_label = white_labels[table_center[1], table_center[0]]

        if table_label == 0:
            # 餐桌中心不在白色区域，寻找最大的白色连通域
            label_areas = {}
            for label in range(1, np.max(white_labels) + 1):
                label_areas[label] = np.sum(white_labels == label)

            if label_areas:
                largest_label = max(label_areas, key=label_areas.get)
                largest_white_mask = (white_labels == largest_label).astype(np.uint8)

                # 检查最大白色区域是否足够大且包含餐桌的一部分
                table_mask = np.zeros_like(gray)
                table_mask[y1:y2, x1:x2] = 1
                table_overlap = np.sum(largest_white_mask & table_mask)

                if table_overlap > 0 and label_areas[largest_label] > total_area * 0.5:
                    return largest_white_mask
        else:
            # 餐桌中心在白色区域，返回该连通域
            main_white_mask = (white_labels == table_label).astype(np.uint8)

            # 检查主要白色区域是否足够大
            if np.sum(main_white_mask) > total_area * 0.5:
                return main_white_mask

        # 如果没有找到合适的白色区域，返回原始掩码
        return dining_mask

    def _truncate_dining_at_doors(self, image, dining_mask, boxes, classes):
        """
        在门处截断餐厅区域

        Args:
            image: 输入图像
            dining_mask: 餐厅区域掩码
            boxes: 所有检测框
            classes: 所有类别

        Returns:
            truncated_mask: 在门处截断后的餐厅区域掩码
        """
        h, w = dining_mask.shape
        truncated_mask = dining_mask.copy()

        # 找到所有门的位置
        door_boxes = []
        for box, cls in zip(boxes, classes):
            if cls == self.config.DOOR_CLASS_ID:
                door_boxes.append(box)

        if not door_boxes:
            return dining_mask

        # 为每个门创建阻挡区域
        door_blocking_mask = np.zeros((h, w), dtype=np.uint8)

        for door_box in door_boxes:
            x1, y1, x2, y2 = door_box

            # 扩展门的阻挡区域，确保完全截断
            # 门的宽度和高度
            door_width = x2 - x1
            door_height = y2 - y1

            # 根据门的方向扩展阻挡区域
            if door_width > door_height:  # 水平门
                # 垂直扩展，创建一个垂直的阻挡带
                expand_height = door_height   
                block_y1 = max(0, y1 - expand_height // 2)
                block_y2 = min(h, y2 + expand_height // 2)
                door_blocking_mask[block_y1:block_y2, x1:x2] = 1
            else:  # 垂直门
                # 水平扩展，创建一个水平的阻挡带
                expand_width = door_width  
                block_x1 = max(0, x1 - expand_width // 2)
                block_x2 = min(w, x2 + expand_width // 2)
                door_blocking_mask[y1:y2, block_x1:block_x2] = 1

        # 从餐厅区域中移除门的阻挡区域
        truncated_mask = truncated_mask & (~door_blocking_mask)

        # 进行连通域分析，保留最大的连通区域
        _, labels = cv2.connectedComponents(truncated_mask)
        if np.max(labels) > 0:
            # 找到最大的连通区域
            label_areas = {}
            for label in range(1, np.max(labels) + 1):
                label_areas[label] = np.sum(labels == label)

            if label_areas:
                largest_label = max(label_areas, key=label_areas.get)
                truncated_mask = (labels == largest_label).astype(np.uint8)

        return truncated_mask

    def extract_dining_room_region(self, image, dining_table_box, boxes=None, classes=None, use_optimization=True):
        """
        基于餐桌位置提取餐厅区域

        Args:
            image: 输入图像 (BGR格式)
            dining_table_box: 餐桌边界框 [x1, y1, x2, y2]
            boxes: 所有检测框列表 (用于门截断)
            classes: 所有类别列表 (用于门截断)
            use_optimization: 是否使用优化方法自动寻找最佳区域

        Returns:
            dining_mask: 餐厅区域掩码
        """
        # 首先尝试优化方法
        if use_optimization:
            optimal_mask = self._find_optimal_dining_region(image, dining_table_box)
            if optimal_mask is not None and np.sum(optimal_mask) >= self.config.AREA_THRESH:
                # 对优化结果进行颜色过滤
                filtered_mask = self._filter_dining_region_by_color(image, optimal_mask, dining_table_box)

                # 在门处截断餐厅区域
                if boxes is not None and classes is not None:
                    filtered_mask = self._truncate_dining_at_doors(image, filtered_mask, boxes, classes)

                return filtered_mask
            print("优化方法失败，使用传统方法")
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        h, w = gray.shape
        x1, y1, x2, y2 = dining_table_box

        # 计算餐桌中心
        table_center = ((x1 + x2) // 2, (y1 + y2) // 2)

        # === 1. 获取可通行区域连通域 ===
        walkable = ((gray > 240) & (cv2.Canny(gray, 50, 150) == 0)).astype(np.uint8)
        _, labels = cv2.connectedComponents(walkable)
        
        # 检查餐桌是否在可通行区域
        table_label = labels[table_center[1], table_center[0]]
        if table_label == 0:
            # 如果餐桌不在可通行区域，使用备用方法
            return self._fallback_dining_extraction(gray, dining_table_box)
        
        connected_mask = (labels == table_label).astype(np.uint8)
        
        # === 2. 生成候选区域 ===
        # 使用自适应方法计算扩展范围
        expand_w, expand_h = self._calculate_adaptive_expansion(image, dining_table_box)
        
        candidate_x1 = max(0, table_center[0] - expand_w // 2)
        candidate_y1 = max(0, table_center[1] - expand_h // 2)
        candidate_x2 = min(w, table_center[0] + expand_w // 2)
        candidate_y2 = min(h, table_center[1] + expand_h // 2)
        
        # === 3. 局部优化 ===
        local_region = walkable[candidate_y1:candidate_y2, candidate_x1:candidate_x2]
        table_local = np.zeros_like(local_region)
        
        # 在局部区域中标记餐桌位置
        lx1 = max(0, x1 - candidate_x1)
        ly1 = max(0, y1 - candidate_y1)
        lx2 = min(local_region.shape[1], x2 - candidate_x1)
        ly2 = min(local_region.shape[0], y2 - candidate_y1)
        table_local[ly1:ly2, lx1:lx2] = 1
        
        # 距离变换和权重计算
        dist_map = cv2.distanceTransform(1 - table_local, cv2.DIST_L2, 5)
        weights = np.exp(-dist_map / (max(expand_w, expand_h) * 0.3))
        
        # 生成局部餐厅区域
        dining_local = ((local_region * weights) > 0.4).astype(np.uint8)
        dining_local = cv2.morphologyEx(dining_local, cv2.MORPH_CLOSE, np.ones((3, 3)))
        dining_local[ly1:ly2, lx1:lx2] = 1  # 强制包含餐桌区域
        
        # === 4. 生成最终掩码 ===
        final_mask = np.zeros_like(walkable)
        final_mask[candidate_y1:candidate_y2, candidate_x1:candidate_x2] = dining_local
        final_mask = final_mask & connected_mask  # 与连通域取交集
        
        # 强制包含餐桌区域
        final_mask[y1:y2, x1:x2] = 1
        
        # === 5. 连通域验证 ===
        _, final_labels = cv2.connectedComponents(final_mask)
        final_label = final_labels[table_center[1], table_center[0]]
        
        if final_label > 0:
            final_mask = (final_labels == final_label).astype(np.uint8)
        else:
            # 保底措施：确保包含餐桌
            final_mask[y1:y2, x1:x2] = 1
            _, final_labels = cv2.connectedComponents(final_mask)
            final_mask = (final_labels == final_labels[table_center[1], table_center[0]]).astype(np.uint8)
        
        # === 6. 颜色区域过滤 ===
        # 过滤掉被灰色区域截断的部分，只保留白色区域的大部分
        filtered_mask = self._filter_dining_region_by_color(image, final_mask, dining_table_box)

        # === 7. 门截断处理 ===
        # 在门处截断餐厅区域
        if boxes is not None and classes is not None:
            filtered_mask = self._truncate_dining_at_doors(image, filtered_mask, boxes, classes)

        return filtered_mask
    
    def _fallback_dining_extraction(self, gray, dining_table_box):
        """
        备用餐厅提取方法，当餐桌不在可通行区域时使用
        """
        h, w = gray.shape
        x1, y1, x2, y2 = dining_table_box
        table_center = ((x1 + x2) // 2, (y1 + y2) // 2)

        # 使用自适应方法计算扩展范围
        expand_w, expand_h = self._calculate_adaptive_expansion(gray, dining_table_box)
        
        rect_x1 = max(0, table_center[0] - expand_w // 2)
        rect_y1 = max(0, table_center[1] - expand_h // 2)
        rect_x2 = min(w, table_center[0] + expand_w // 2)
        rect_y2 = min(h, table_center[1] + expand_h // 2)
        
        # 创建矩形掩码
        fallback_mask = np.zeros((h, w), dtype=np.uint8)
        fallback_mask[rect_y1:rect_y2, rect_x1:rect_x2] = 1
        
        # 与可通行区域取交集
        walkable = (gray > 240).astype(np.uint8)
        fallback_mask = fallback_mask & walkable

        # 颜色区域过滤（备用方法中，gray就是原图的灰度版本）
        # 需要重新构造image参数
        if len(gray.shape) == 2:
            # 如果输入是灰度图，创建一个伪彩色图像用于过滤
            pseudo_image = cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)
            filtered_mask = self._filter_dining_region_by_color(pseudo_image, fallback_mask, dining_table_box)
        else:
            filtered_mask = self._filter_dining_region_by_color(gray, fallback_mask, dining_table_box)

        return filtered_mask
    
    def process_dining_rooms_in_labels(self, image, labels_im, boxes, classes, room_type_for_label):
        """
        在已分割的房间标签中处理餐厅区域
        
        Args:
            image: 输入图像
            labels_im: 房间分割标签图
            boxes: 检测框列表
            classes: 类别列表
            room_type_for_label: 房间类型映射
            
        Returns:
            updated_labels_im: 更新后的标签图
            updated_room_type_for_label: 更新后的房间类型映射
        """
        # 找到所有餐桌
        dining_table_boxes = []
        for box, cls in zip(boxes, classes):
            if cls == self.config.DINING_TABLE_CLASS_ID:
                dining_table_boxes.append(box)
        
        if not dining_table_boxes:
            return labels_im, room_type_for_label
        
        updated_labels_im = labels_im.copy()
        updated_room_type_for_label = room_type_for_label.copy()
        
        # 为每个餐桌生成精确的餐厅区域
        max_label = np.max(updated_labels_im)

        for i, dining_box in enumerate(dining_table_boxes):
            try:
                # 首先检查餐桌是否处于单独的连通域
                separate_region = self._check_if_table_in_separate_region(image, dining_box, boxes, classes)

                if separate_region is not None:
                    # 餐桌处于单独连通域且无其他家具，直接使用该连通域作为餐厅
                    print(f"餐桌 {i+1} 处于单独连通域，直接设置为餐厅")
                    dining_mask = separate_region

                    # 对于单独连通域，直接设置为餐厅，无需复杂的重叠处理
                    new_label = max_label + i + 1

                    # 简单替换：清除该区域内的所有现有标签
                    overlap_labels = set(updated_labels_im[dining_mask > 0])
                    overlap_labels.discard(0)

                    for overlap_label in overlap_labels:
                        if overlap_label in updated_room_type_for_label:
                            del updated_room_type_for_label[overlap_label]

                    # 设置新的餐厅区域
                    updated_labels_im[dining_mask > 0] = new_label
                    updated_room_type_for_label[new_label] = "diningroom"

                    print(f"  已设置单独连通域为餐厅，标签: {new_label}")
                    continue  # 跳过后续的复杂处理
                else:
                    # 餐桌不在单独连通域，使用复杂的分割方法
                    print(f"餐桌 {i+1} 不在单独连通域，使用复杂分割方法")
                    dining_mask = self.extract_dining_room_region(image, dining_box, boxes, classes)

                if np.sum(dining_mask) < self.config.AREA_THRESH:
                    continue
                
                print(f"创建餐厅区域 {i+1}，面积: {np.sum(dining_mask)} 像素")
                
                # 分配新的标签
                new_label = max_label + i + 1

                # 根据是否为单独连通域采用不同的重叠处理策略
                if separate_region is not None:
                    # 单独连通域：简单替换重叠区域
                    print(f"  单独连通域餐厅，直接替换重叠区域")
                    overlap_labels = set(updated_labels_im[dining_mask > 0])
                    overlap_labels.discard(0)

                    for overlap_label in overlap_labels:
                        overlap_room_type = updated_room_type_for_label.get(overlap_label, "unknow")
                        print(f"    替换重叠区域 {overlap_label} ({overlap_room_type})")
                        # 直接删除重叠的房间标签
                        if overlap_label in updated_room_type_for_label:
                            del updated_room_type_for_label[overlap_label]
                else:
                    # 复杂分割：智能处理重叠
                    overlap_labels = set(updated_labels_im[dining_mask > 0])
                    overlap_labels.discard(0)  # 移除背景标签

                    for overlap_label in overlap_labels:
                        # 计算重叠比例
                        overlap_mask = (updated_labels_im == overlap_label) & (dining_mask > 0)
                        overlap_ratio = np.sum(overlap_mask) / np.sum(updated_labels_im == overlap_label)

                        # 获取重叠区域的房间类型
                        overlap_room_type = updated_room_type_for_label.get(overlap_label, "unknow")

                        # 智能处理重叠：
                        # 1. 如果是客厅且重叠比例不太高，保留客厅的非重叠部分
                        # 2. 如果是其他类型房间且重叠比例高，则替换
                        print(f"  处理重叠区域 {overlap_label} ({overlap_room_type}), 重叠比例: {overlap_ratio:.2f}")

                        if overlap_room_type == "livingroom" and overlap_ratio < 0.6:
                            # 保留客厅的非重叠部分，重叠区域将被餐厅占用
                            print(f"    保留客厅非重叠部分，重叠区域归餐厅")
                            # 不需要清除，让餐厅直接覆盖重叠区域
                        elif overlap_ratio > 0.5:  # 提高阈值到50%
                            # 完全替换高重叠的非客厅区域
                            print(f"    完全替换高重叠区域 ({overlap_room_type})")
                            updated_labels_im[updated_labels_im == overlap_label] = 0
                            if overlap_label in updated_room_type_for_label:
                                del updated_room_type_for_label[overlap_label]
                        else:
                            # 低重叠情况：重叠区域归餐厅，保留原房间的其余部分
                            print(f"    部分重叠，重叠区域归餐厅")
                            # 不需要清除，让餐厅直接覆盖重叠区域
                
                # 设置新的餐厅区域
                updated_labels_im[dining_mask > 0] = new_label
                updated_room_type_for_label[new_label] = "diningroom"
                
            except Exception as e:
                print(f"处理餐厅区域时出错: {e}")
                continue
        
        # 填补房间之间的小空隙
        updated_labels_im = self._fill_gaps_between_rooms(updated_labels_im, updated_room_type_for_label)

        # 识别厨房：找到距离餐厅最近的未知房间
        updated_room_type_for_label = self._identify_kitchen_near_dining(
            updated_labels_im, updated_room_type_for_label, boxes, classes
        )

        # 最终检查：确保只有一个厨房区域
        kitchen_labels = [label for label, room_type in updated_room_type_for_label.items()
                         if room_type == "kitchen"]

        if len(kitchen_labels) > 1:
            print(f"警告：检测到多个厨房区域 {kitchen_labels}，只保留第一个")
            # 只保留第一个厨房，其他的改回未知
            for i, kitchen_label in enumerate(kitchen_labels):
                if i > 0:  # 保留第一个，删除其他的
                    updated_room_type_for_label[kitchen_label] = "unknow"
                    print(f"  将厨房标签 {kitchen_label} 改回未知房间")

        return updated_labels_im, updated_room_type_for_label

    def _should_create_dining_room(self, dining_mask, existing_labels, existing_room_types, dining_box=None):
        """
        判断是否应该创建独立的餐厅区域

        Args:
            dining_mask: 餐厅区域掩码
            existing_labels: 现有房间标签
            existing_room_types: 现有房间类型映射
            dining_box: 餐桌边界框 (可选，暂未使用)

        Returns:
            bool: 是否应该创建餐厅区域
        """
        # 检查餐厅区域大小
        dining_area = np.sum(dining_mask)
        if dining_area < self.config.AREA_THRESH * 2:  # 餐厅区域太小
            return False

        # 检查是否与现有客厅重叠过多
        overlap_with_living = 0
        for label, room_type in existing_room_types.items():
            if room_type == "livingroom":
                living_mask = (existing_labels == label)
                overlap = np.sum(living_mask & (dining_mask > 0))
                overlap_with_living += overlap

        # 如果餐厅区域大部分与客厅重叠，可能不需要单独分割
        if overlap_with_living > dining_area * 0.8:
            return False

        return True

    def _fill_gaps_between_rooms(self, labels_im, room_type_for_label=None):
        """
        填补房间之间的小空隙

        Args:
            labels_im: 房间标签图
            room_type_for_label: 房间类型映射 (可选，暂未使用)

        Returns:
            filled_labels_im: 填补空隙后的标签图
        """
        filled_labels_im = labels_im.copy()
        h, w = labels_im.shape

        # 找到所有背景像素（值为0的像素）
        background_mask = (labels_im == 0)

        # 对背景区域进行连通域分析
        _, bg_labels = cv2.connectedComponents(background_mask.astype(np.uint8))

        # 处理每个背景连通域
        for bg_label in range(1, np.max(bg_labels) + 1):
            bg_region = (bg_labels == bg_label)
            bg_area = np.sum(bg_region)

            # 只处理小的空隙（面积小于阈值）
            if bg_area > self.config.AREA_THRESH // 2:  # 如果空隙太大，保持不变
                continue

            # 找到这个空隙周围的房间标签
            # 膨胀空隙区域来找邻近的房间
            kernel = np.ones((3, 3), np.uint8)
            dilated_bg = cv2.dilate(bg_region.astype(np.uint8), kernel, iterations=1)

            # 找到膨胀区域内的非背景标签
            neighbor_labels = set()
            for y in range(h):
                for x in range(w):
                    if dilated_bg[y, x] and labels_im[y, x] > 0:
                        neighbor_labels.add(labels_im[y, x])

            if neighbor_labels:
                # 选择最常见的邻近标签来填充空隙
                neighbor_counts = {}
                for label in neighbor_labels:
                    # 计算每个邻近标签在膨胀区域内的像素数
                    count = np.sum((labels_im == label) & (dilated_bg > 0))
                    neighbor_counts[label] = count

                # 选择像素数最多的标签
                best_label = max(neighbor_counts, key=neighbor_counts.get)
                filled_labels_im[bg_region] = best_label

        return filled_labels_im

    def _identify_kitchen_near_dining(self, labels_im, room_type_for_label, boxes, classes):
        """
        通过找到距离餐桌椅边界最近的门，将该门所在的未知区域设置为厨房

        Args:
            labels_im: 房间标签图
            room_type_for_label: 房间类型映射
            boxes: 检测框列表
            classes: 类别列表

        Returns:
            updated_room_type_for_label: 更新后的房间类型映射
        """
        updated_room_type_for_label = room_type_for_label.copy()

        # 检查是否已经有厨房区域
        existing_kitchens = [label for label, room_type in room_type_for_label.items()
                           if room_type == "kitchen"]

        if existing_kitchens:
            print(f"已存在厨房区域（标签: {existing_kitchens}），跳过厨房识别")
            return updated_room_type_for_label

        # 找到所有餐桌椅的边界框
        dining_table_boxes = []
        for box, cls in zip(boxes, classes):
            if cls == self.config.DINING_TABLE_CLASS_ID:
                dining_table_boxes.append(box)

        if not dining_table_boxes:
            print("未找到餐桌椅，无法识别厨房")
            return updated_room_type_for_label

        # 找到所有门的位置
        door_boxes = []
        for box, cls in zip(boxes, classes):
            if cls == self.config.DOOR_CLASS_ID:
                door_boxes.append(box)

        if not door_boxes:
            print("未找到门，无法识别厨房")
            return updated_room_type_for_label

        # 找到所有未知房间
        unknown_labels = [label for label, room_type in room_type_for_label.items()
                         if room_type == "unknow"]

        if not unknown_labels:
            print("未找到未知房间，无法识别厨房")
            return updated_room_type_for_label

        print(f"找到 {len(dining_table_boxes)} 个餐桌椅、{len(door_boxes)} 个门和 {len(unknown_labels)} 个未知房间")

        # 找到距离餐桌椅边界最近且方向符合要求的门
        closest_door = None
        min_door_distance = float('inf')

        for door_box in door_boxes:
            dx1, dy1, dx2, dy2 = door_box
            door_center_x = (dx1 + dx2) // 2
            door_center_y = (dy1 + dy2) // 2

            # 计算门到所有餐桌椅的最短距离，并检查方向
            min_dist_to_tables = float('inf')
            valid_orientation = False

            for table_box in dining_table_boxes:
                tx1, ty1, tx2, ty2 = table_box

                # 检查门的方向是否符合要求
                if self._check_door_orientation_valid(door_box, table_box):
                    valid_orientation = True

                    # 计算门边界框到餐桌边界框的最短距离
                    # 水平距离
                    if dx2 < tx1:  # 门在餐桌左边
                        dx = tx1 - dx2
                    elif dx1 > tx2:  # 门在餐桌右边
                        dx = dx1 - tx2
                    else:  # 门与餐桌在水平方向有重叠
                        dx = 0

                    # 垂直距离
                    if dy2 < ty1:  # 门在餐桌上方
                        dy = ty1 - dy2
                    elif dy1 > ty2:  # 门在餐桌下方
                        dy = dy1 - ty2
                    else:  # 门与餐桌在垂直方向有重叠
                        dy = 0

                    # 计算欧几里得距离
                    distance = np.sqrt(dx**2 + dy**2)
                    min_dist_to_tables = min(min_dist_to_tables, distance)
                else:
                    print(f"    门方向不符合要求，跳过")

            # 只考虑方向符合要求的门
            if valid_orientation and min_dist_to_tables < min_door_distance:
                min_door_distance = min_dist_to_tables
                closest_door = (door_box, door_center_x, door_center_y)

        if closest_door is None:
            print("未找到方向符合要求的门（左右门需竖直，上下门需水平）")
            return updated_room_type_for_label

        door_box, door_center_x, door_center_y = closest_door
        print(f"找到距离餐桌最近的门，距离: {min_door_distance:.1f} 像素")

        # 检查距离是否在阈值内
        if min_door_distance > self.config.KITCHEN_DISTANCE_THRESHOLD:
            print(f"最近的门距离餐桌 {min_door_distance:.1f} 像素，超过阈值 {self.config.KITCHEN_DISTANCE_THRESHOLD}，不设置厨房")
            return updated_room_type_for_label

        # 找到该门所在的未知房间
        kitchen_candidate = None
        for unknown_label in unknown_labels:
            unknown_mask = (labels_im == unknown_label)

            # 检查门的中心是否在该未知房间内
            if unknown_mask[door_center_y, door_center_x]:
                kitchen_candidate = unknown_label
                break

        # 如果门不在任何未知房间内，寻找最近的未知房间
        if kitchen_candidate is None:
            min_dist_to_unknown = float('inf')
            for unknown_label in unknown_labels:
                unknown_mask = (labels_im == unknown_label)
                y_coords, x_coords = np.where(unknown_mask)

                if len(y_coords) == 0:
                    continue

                # 计算门到未知房间边界的最短距离
                room_min_x, room_max_x = np.min(x_coords), np.max(x_coords)
                room_min_y, room_max_y = np.min(y_coords), np.max(y_coords)

                # 计算门中心到房间边界的距离
                if door_center_x < room_min_x:
                    dx = room_min_x - door_center_x
                elif door_center_x > room_max_x:
                    dx = door_center_x - room_max_x
                else:
                    dx = 0

                if door_center_y < room_min_y:
                    dy = room_min_y - door_center_y
                elif door_center_y > room_max_y:
                    dy = door_center_y - room_max_y
                else:
                    dy = 0

                distance = np.sqrt(dx**2 + dy**2)

                if distance < min_dist_to_unknown:
                    min_dist_to_unknown = distance
                    kitchen_candidate = unknown_label

        # 设置厨房（确保只有一个厨房）
        if kitchen_candidate is not None:
            # 再次检查是否已有厨房（防止并发或重复设置）
            current_kitchens = [label for label, room_type in updated_room_type_for_label.items()
                              if room_type == "kitchen"]

            if current_kitchens:
                print(f"已存在厨房区域（标签: {current_kitchens}），不再设置新的厨房")
            else:
                # 检查厨房候选区域是否被门包围且面积过小
                is_surrounded = self._check_room_surrounded_by_doors(labels_im, kitchen_candidate, boxes, classes)
                kitchen_mask = (labels_im == kitchen_candidate)
                kitchen_area = np.sum(kitchen_mask)

                if is_surrounded and kitchen_area < self.config.KITCHEN_MIN_AREA_THRESHOLD:
                    print(f"候选厨房区域 {kitchen_candidate} 三边都有门且面积过小（{kitchen_area} < {self.config.KITCHEN_MIN_AREA_THRESHOLD}），不设置为厨房")
                else:
                    updated_room_type_for_label[kitchen_candidate] = "kitchen"
                    print(f"将标签 {kitchen_candidate} 的未知房间设置为厨房（基于最近门的位置），面积: {kitchen_area}")
        else:
            print("未找到合适的厨房候选区域")

        return updated_room_type_for_label

    def _check_room_surrounded_by_doors(self, labels_im, room_label, boxes, classes):
        """
        检查房间的三边是否都有门

        Args:
            labels_im: 房间标签图
            room_label: 房间标签
            boxes: 检测框列表
            classes: 类别列表

        Returns:
            bool: 如果三边都有门返回True，否则返回False
        """
        room_mask = (labels_im == room_label)
        if np.sum(room_mask) == 0:
            return False

        # 获取房间的边界框
        y_coords, x_coords = np.where(room_mask)
        room_min_x, room_max_x = np.min(x_coords), np.max(x_coords)
        room_min_y, room_max_y = np.min(y_coords), np.max(y_coords)

        # 定义房间的四个边界区域（扩展一些像素来检测附近的门）
        expand_pixels = 30  # 扩展像素数

        # 上边界区域
        top_region = (room_min_y - expand_pixels, room_max_y, room_min_x, room_max_x)
        # 下边界区域
        bottom_region = (room_min_y, room_max_y + expand_pixels, room_min_x, room_max_x)
        # 左边界区域
        left_region = (room_min_y, room_max_y, room_min_x - expand_pixels, room_max_x)
        # 右边界区域
        right_region = (room_min_y, room_max_y, room_min_x, room_max_x + expand_pixels)

        regions = [top_region, bottom_region, left_region, right_region]
        region_names = ["上边", "下边", "左边", "右边"]

        doors_on_sides = 0

        # 检查每个边界区域是否有门
        for i, (min_y, max_y, min_x, max_x) in enumerate(regions):
            has_door = False

            for box, cls in zip(boxes, classes):
                if cls == self.config.DOOR_CLASS_ID:
                    door_x1, door_y1, door_x2, door_y2 = box
                    door_center_x = (door_x1 + door_x2) // 2
                    door_center_y = (door_y1 + door_y2) // 2

                    # 检查门是否在当前边界区域内
                    if (min_x <= door_center_x <= max_x and
                        min_y <= door_center_y <= max_y):
                        has_door = True
                        print(f"    在{region_names[i]}发现门")
                        break

            if has_door:
                doors_on_sides += 1

        print(f"  房间标签 {room_label} 有 {doors_on_sides} 边有门")

        # 如果三边或以上都有门，认为被门包围
        return doors_on_sides >= 3

    def _check_door_orientation_valid(self, door_box, table_box):
        """
        检查门的方向是否符合要求

        Args:
            door_box: 门的边界框 [x1, y1, x2, y2]
            table_box: 餐桌的边界框 [x1, y1, x2, y2]

        Returns:
            bool: 如果门的方向符合要求返回True，否则返回False
        """
        dx1, dy1, dx2, dy2 = door_box
        tx1, ty1, tx2, ty2 = table_box

        # 计算门的尺寸
        door_width = dx2 - dx1
        door_height = dy2 - dy1

        # 计算门和餐桌的中心点
        door_center_x = (dx1 + dx2) // 2
        door_center_y = (dy1 + dy2) // 2
        table_center_x = (tx1 + tx2) // 2
        table_center_y = (ty1 + ty2) // 2

        # 判断门相对于餐桌的位置
        dx = door_center_x - table_center_x
        dy = door_center_y - table_center_y

        # 判断门主要在餐桌的哪个方向
        if abs(dx) > abs(dy):
            # 门在餐桌的左右两边
            if dx < 0:
                position = "左边"
            else:
                position = "右边"

            # 门应该是竖着的（高度 > 宽度）
            is_vertical = door_height > door_width
            print(f"    门在餐桌{position}，门尺寸: {door_width}x{door_height}，是否竖直: {is_vertical}")
            return is_vertical
        else:
            # 门在餐桌的上下两边
            if dy < 0:
                position = "上边"
            else:
                position = "下边"

            # 门应该是横着的（宽度 > 高度）
            is_horizontal = door_width > door_height
            print(f"    门在餐桌{position}，门尺寸: {door_width}x{door_height}，是否水平: {is_horizontal}")
            return is_horizontal
