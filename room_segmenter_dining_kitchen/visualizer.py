import cv2
import numpy as np
from config import Config
from scipy.ndimage import binary_fill_holes, binary_dilation

class ResultVisualizer:
    """结果可视化类"""
    
    def __init__(self):
        self.config = Config()

    def draw_rooms(self, image, labels_im, room_type_for_label, boxes, classes):
        """绘制房间区域"""
        vis_image = image.copy()
        color_mask = np.zeros_like(image)

        for i, room_type in room_type_for_label.items():
            mask = (labels_im == i)
            mask_filled = binary_fill_holes(mask)
            mask_uint8 = (mask_filled * 255).astype(np.uint8)
            contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            filled_mask = np.zeros_like(mask_uint8)
            cv2.drawContours(filled_mask, contours, -1, 255, thickness=-1)
            filled_mask_bool = filled_mask.astype(bool)

            # 应用半透明颜色
            color = self.config.ROOM_COLOR_MAP[room_type]
            vis_image[filled_mask_bool] = cv2.addWeighted(
                vis_image, self.config.IMAGE_BLEND_ALPHA,
                np.full_like(image, color), self.config.COLOR_BLEND_ALPHA, 0
            )[filled_mask_bool]
            color_mask[filled_mask_bool] = color

            # 添加房间类型标签
            # ys, xs = np.where(filled_mask_bool)
            # if len(xs) > 0 and len(ys) > 0:
            #     cx, cy = int(np.mean(xs)), int(np.mean(ys))
            #     cv2.putText(vis_image, room_type,
            #                (cx - self.config.TEXT_OFFSET_X, cy),
            #                self.config.FONT, self.config.ROOM_LABEL_FONT_SIZE,
            #                self.config.TEXT_COLOR, self.config.TEXT_THICKNESS)
        # 绘制门框
        for (x1, y1, x2, y2), cls in zip(boxes, classes):
            if cls == self.config.DOOR_CLASS_ID:  # 门
                cv2.rectangle(vis_image, (x1, y1), (x2, y2), self.config.OBJECT_COLOR_MAP["door"], 2)
                 
        return vis_image, color_mask

    def draw_boxes(self, image, boxes, classes, confidence_scores=None):
        """绘制检测框"""
        boxed_image = image.copy()
        for idx, ((x1, y1, x2, y2), cls) in enumerate(zip(boxes, classes)):
            label = self.config.CLASS_NAMES.get(cls, str(cls))
            color = self.config.OBJECT_COLOR_MAP.get(label, (0, 255, 255))
            cv2.rectangle(boxed_image, (x1, y1), (x2, y2), color, self.config.BOX_THICKNESS)

            if label != self.config.CLASS_NAMES[self.config.DOOR_CLASS_ID]:
                cx = (x1 + x2) // 2
                cy = (y1 + y2) // 2
                cv2.circle(boxed_image, (cx, cy), self.config.CENTER_POINT_RADIUS,
                          self.config.CENTER_POINT_COLOR, self.config.CENTER_POINT_THICKNESS)

            if confidence_scores:
                text = f"{confidence_scores[idx]:.2f}"
                (tw, th), _ = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX,
                                            self.config.CONFIDENCE_FONT_SIZE,
                                            self.config.CONFIDENCE_TEXT_THICKNESS)
                cv2.rectangle(boxed_image,
                             (x1, y1 - th - self.config.CONFIDENCE_TEXT_OFFSET),
                             (x1 + tw, y1), color, -1)
                cv2.putText(boxed_image, text, (x1, y1 - self.config.CONFIDENCE_TEXT_OFFSET),
                           cv2.FONT_HERSHEY_SIMPLEX, self.config.CONFIDENCE_FONT_SIZE,
                           self.config.TEXT_COLOR, self.config.CONFIDENCE_TEXT_THICKNESS)

        return boxed_image

    def draw_legend(self, image, legend_height=None):
        """绘制图例"""
        if legend_height is None:
            legend_height = self.config.LEGEND_HEIGHT

        legend_font = self.config.LEGEND_FONT_SIZE
        y_offset_2 = self.config.LEGEND_Y_OFFSET_2
        y_offset_3 = self.config.LEGEND_Y_OFFSET_3
        y_offset_4 = self.config.LEGEND_Y_OFFSET_4
        x_step = self.config.LEGEND_X_STEP

        # 绘制家具类别图例
        for i, label in enumerate(["bed_grounded", "bed_highleg"]):
            color = self.config.OBJECT_COLOR_MAP[label]
            x = self.config.LEGEND_RECT_X_OFFSET + i * x_step
            cv2.rectangle(image, (x, y_offset_2 - 15),
                         (x + self.config.LEGEND_RECT_WIDTH, y_offset_2 + 5), color, -1)
            cv2.putText(image, label,
                       (x + self.config.LEGEND_TEXT_X_OFFSET, y_offset_2 + self.config.LEGEND_TEXT_Y_OFFSET),
                       self.config.FONT, legend_font, self.config.TEXT_COLOR, 1)

        for i, label in enumerate(["sofa_grounded", "sofa_highleg"]):
            color = self.config.OBJECT_COLOR_MAP[label]
            x = self.config.LEGEND_RECT_X_OFFSET + i * x_step
            cv2.rectangle(image, (x, y_offset_3 - 15),
                         (x + self.config.LEGEND_RECT_WIDTH, y_offset_3 + 5), color, -1)
            cv2.putText(image, label,
                       (x + self.config.LEGEND_TEXT_X_OFFSET, y_offset_3 + self.config.LEGEND_TEXT_Y_OFFSET),
                       self.config.FONT, legend_font, self.config.TEXT_COLOR, 1)

        for i, label in enumerate(["door", "dining_table_set"]):
            color = self.config.OBJECT_COLOR_MAP[label]
            x = self.config.LEGEND_RECT_X_OFFSET + i * x_step
            cv2.rectangle(image, (x, y_offset_4 - 15),
                         (x + self.config.LEGEND_RECT_WIDTH, y_offset_4 + 5), color, -1)
            cv2.putText(image, label,
                       (x + self.config.LEGEND_TEXT_X_OFFSET, y_offset_4 + self.config.LEGEND_TEXT_Y_OFFSET),
                       self.config.FONT, legend_font, self.config.TEXT_COLOR, 1)

    def create_final_result(self, vis_image, boxed_image, gt_image):
        """创建最终结果图像
        
        参数:
            vis_image: 分割可视化图像
            boxed_image: 检测结果图像
            gt_image: 真实标注图像
            
        返回:
            final_image: 包含三视图和文字标注的最终图像
        """
        vis_h, vis_w = vis_image.shape[:2]
        legend_height = self.config.LEGEND_HEIGHT
        text_height = self.config.BOTTOM_TEXT_HEIGHT  # 底部文字区域高度
        
        # 创建最终图像 (增加底部文字空间)
        final_height = vis_h + legend_height + text_height
        final_image = np.ones((final_height, vis_w * 3, 3), dtype=np.uint8) * self.config.FINAL_IMAGE_BACKGROUND_COLOR

        # 排列三张结果图
        final_image[legend_height:legend_height+vis_h, :vis_w] = vis_image        # 分割可视化
        final_image[legend_height:legend_height+vis_h, vis_w:2*vis_w] = boxed_image  # 检测结果
        final_image[legend_height:legend_height+vis_h, 2*vis_w:] = gt_image       # 真实标注

        # 添加分隔线
        cv2.line(final_image, (vis_w, legend_height), (vis_w, legend_height+vis_h),
                self.config.SEPARATOR_LINE_COLOR, self.config.SEPARATOR_LINE_THICKNESS)
        cv2.line(final_image, (2*vis_w, legend_height), (2*vis_w, legend_height+vis_h),
                self.config.SEPARATOR_LINE_COLOR, self.config.SEPARATOR_LINE_THICKNESS)

        # 添加图例
        self.draw_legend(final_image, legend_height)
        
        # 添加底部文字描述
        self._draw_bottom_text(final_image, vis_w, vis_h, legend_height, text_height)

        return final_image

    def _draw_bottom_text(self, image, vis_w, vis_h, legend_height, text_height):
        """在底部添加文字描述"""
        # 计算文字位置
        text_y = legend_height + vis_h + int(text_height * 0.7)
        text_positions = [
            (int(vis_w * 0.5)),    # 第一列中心
            (int(vis_w * 1.5)),    # 第二列中心
            (int(vis_w * 2.5))   # 第三列中心
        ]
        texts = ["SegmentationRoom", "DetectionResult", "GroundTruth"]
        
        # 绘制文字
        for pos, text in zip(text_positions, texts):
            text_size = cv2.getTextSize(text, self.config.FONT, 
                                    self.config.BOTTOM_TEXT_FONT_SIZE,
                                    self.config.BOTTOM_TEXT_THICKNESS)[0]
            text_x = pos - text_size[0] // 2
            cv2.putText(image, text, (text_x, text_y),
                    self.config.FONT, self.config.BOTTOM_TEXT_FONT_SIZE,
                    self.config.BOTTOM_TEXT_COLOR, self.config.BOTTOM_TEXT_THICKNESS)