#!/usr/bin/env python3
"""
测试餐厅区域分割功能
"""

import os
import cv2
import numpy as np
from pathlib import Path
from dining_processor import DiningRoomProcessor
from config import Config

def test_dining_room_extraction():
    """测试餐厅区域提取功能"""
    
    # 创建测试用的餐厅处理器
    dining_processor = DiningRoomProcessor()
    config = Config()
    
    print("=== 餐厅区域分割测试 ===")
    print(f"餐桌类别ID: {config.DINING_TABLE_CLASS_ID}")
    print(f"餐厅颜色: {config.ROOM_COLOR_MAP['diningroom']}")
    
    # 测试图像路径（可以根据实际情况修改）
    test_image_path = "/home/<USER>/panpan/code/ultralytics-main/datasets/data4/images/dab1604a-shunzao_5897.png"
    
    if not os.path.exists(test_image_path):
        print(f"⚠️ 测试图像不存在: {test_image_path}")
        print("请修改test_image_path为实际存在的图像路径")
        return
    
    # 加载测试图像
    image = cv2.imread(test_image_path)
    if image is None:
        print(f"⚠️ 无法加载图像: {test_image_path}")
        return
    
    print(f"✅ 成功加载图像: {test_image_path}")
    print(f"图像尺寸: {image.shape}")
    
    # 模拟餐桌检测框（基于YOLO格式转换）
    # 这里使用示例中的餐桌标签
    h, w = image.shape[:2]
    
    # YOLO格式: (class_id, cx, cy, bw, bh) - 归一化坐标
    dining_table_yolo = (5, 0.4201824401368301, 0.7314709236031927, 0.14025085518814137, 0.1630558722919042)
    
    # 转换为像素坐标
    _, cx, cy, bw, bh = dining_table_yolo
    x1 = int((cx - bw/2) * w)
    y1 = int((cy - bh/2) * h)
    x2 = int((cx + bw/2) * w)
    y2 = int((cy + bh/2) * h)
    
    dining_table_box = [x1, y1, x2, y2]
    print(f"餐桌边界框: {dining_table_box}")
    
    try:
        # 提取餐厅区域
        dining_mask = dining_processor.extract_dining_room_region(image, dining_table_box)
        
        print(f"✅ 成功提取餐厅区域")
        print(f"餐厅区域面积: {np.sum(dining_mask)} 像素")
        
        # 创建可视化结果
        vis_image = image.copy()
        
        # 绘制餐厅区域（绿色覆盖）
        overlay = vis_image.copy()
        overlay[dining_mask > 0] = [0, 255, 0]  # 绿色
        vis_image = cv2.addWeighted(vis_image, 0.7, overlay, 0.3, 0)
        
        # 绘制餐桌边界框（红色）
        cv2.rectangle(vis_image, (x1, y1), (x2, y2), (0, 0, 255), 2)
        cv2.putText(vis_image, "Dining Table", (x1, y1-10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
        
        # 添加标题
        cv2.putText(vis_image, "Dining Room Segmentation Test", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # 保存结果
        output_dir = "test_output"
        os.makedirs(output_dir, exist_ok=True)
        
        output_path = os.path.join(output_dir, "dining_room_test_result.png")
        cv2.imwrite(output_path, vis_image)
        
        # 保存掩码
        mask_path = os.path.join(output_dir, "dining_room_mask.png")
        cv2.imwrite(mask_path, dining_mask * 255)
        
        print(f"✅ 测试结果已保存:")
        print(f"   可视化结果: {output_path}")
        print(f"   掩码文件: {mask_path}")
        
    except Exception as e:
        print(f"❌ 餐厅区域提取失败: {e}")
        import traceback
        traceback.print_exc()

def test_integration_with_room_segmenter():
    """测试与房间分割器的集成"""
    print("\n=== 集成测试 ===")
    
    try:
        from main import RoomSegmenter
        
        # 测试路径（根据实际情况修改）
        IMAGE_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0708/images/val"
        LABEL_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0708/labels/val"
        MODEL_PATH = "/home/<USER>/panpan/code/ultralytics-main/runs/detect/train2/weights/best.pt"
        OUTPUT_DIR = "test_output"
        
        # 检查路径是否存在
        if not os.path.exists(IMAGE_DIR):
            print(f"⚠️ 图像目录不存在: {IMAGE_DIR}")
            return
        
        if not os.path.exists(MODEL_PATH):
            print(f"⚠️ 模型文件不存在: {MODEL_PATH}")
            return
        
        print("✅ 开始集成测试...")
        
        # 创建房间分割器
        segmenter = RoomSegmenter(IMAGE_DIR, LABEL_DIR, MODEL_PATH, OUTPUT_DIR)
        
        # 处理第一张图像作为测试
        from utils import get_image_files, get_label_path
        image_files = get_image_files(IMAGE_DIR)
        
        if not image_files:
            print("⚠️ 没有找到图像文件")
            return
        
        # 处理第一张图像
        first_image = image_files[0]
        img_path = os.path.join(IMAGE_DIR, first_image)
        label_path = get_label_path(first_image, LABEL_DIR)
        
        if os.path.exists(label_path):
            print(f"处理测试图像: {first_image}")
            segmenter.process_single_image(img_path, label_path)
            print("✅ 集成测试完成")
        else:
            print(f"⚠️ 标签文件不存在: {label_path}")
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 运行测试
    test_dining_room_extraction()
    test_integration_with_room_segmenter()
    
    print("\n=== 测试完成 ===")
    print("如果测试成功，您应该在test_output目录中看到以下文件:")
    print("- dining_room_test_result.png: 餐厅分割可视化结果")
    print("- dining_room_mask.png: 餐厅区域掩码")
    print("- *_final_result.png: 完整的房间分割结果")
    print("\n=== 新功能说明 ===")
    print("1. 餐厅区域分割：基于餐桌位置的精确分割")
    print("2. 单独连通域检测：如果餐桌在独立区域，直接设为餐厅")
    print("3. 颜色区域过滤：只保留白色区域，去除灰色截断部分")
    print("4. 门截断处理：在门处截断餐厅区域")
    print("5. 厨房识别：基于门隔断连通域分析，确保只有一个厨房")
    print("6. 空隙填补：填补房间之间的小空隙")
